# 白菜出行 - 顺风车出行项目

基于 Taro 框架开发的跨平台顺风车出行应用，支持微信小程序、H5、App 等多端运行。

## 项目特色

- 🚗 **完整的顺风车功能**：支持车主发布行程和乘客预订座位
- 📱 **多端适配**：基于 Taro 框架，一套代码多端运行
- 🎨 **精美UI设计**：参考主流出行应用设计，用户体验优秀
- 🗺️ **地图集成**：支持位置选择、路线规划等地图功能
- 💬 **实时聊天**：司机和乘客可以实时沟通
- 🔐 **用户认证**：支持手机号登录注册，用户身份认证

## 技术栈

- **框架**: Taro 4.0.9
- **语言**: TypeScript
- **样式**: Less
- **状态管理**: React Hooks
- **构建工具**: Vite
- **包管理**: npm/yarn

## 项目结构

```
src/
├── app.config.ts          # 应用配置
├── app.less              # 全局样式
├── app.ts                # 应用入口
├── assets/               # 静态资源
│   └── icons/           # 图标资源
├── data/                # 模拟数据
│   └── mockData.ts      # 模拟API数据
├── pages/               # 页面组件
│   ├── home/           # 首页
│   ├── login/          # 登录页
│   ├── register/       # 注册页
│   ├── publish/        # 发布行程页
│   ├── order/          # 订单页
│   ├── profile/        # 个人中心
│   ├── trip-detail/    # 行程详情页
│   ├── search-result/  # 搜索结果页
│   └── chat/           # 聊天页
├── services/           # API服务
│   └── tripApi.ts      # 行程相关API
├── types/              # 类型定义
│   └── trip.ts         # 业务类型定义
└── utils/              # 工具函数
    └── common.ts       # 通用工具函数
```

## 主要功能

### 🏠 首页
- 用户信息展示（位置、天气）
- 出行方式选择（顺风车、打车、城际专车）
- 地点选择（起点、终点）
- 快捷服务网格（10个常用服务）
- 福利活动展示

### 🚗 发布行程
- 行程类型选择（车找人/人找车）
- 路线信息设置
- 出发时间选择
- 座位数和价格设置
- 车辆信息填写（车主）
- 出行要求选择
- 备注说明

### 🔍 搜索结果
- 行程列表展示
- 司机信息展示
- 价格和座位信息
- 车辆信息展示
- 出行要求标签
- 一键预订功能

### 📋 行程详情
- 详细的行程信息
- 司机详细资料
- 车辆详细信息
- 路线和时间信息
- 座位选择器
- 预订功能
- 联系司机

### 💬 聊天功能
- 实时消息收发
- 消息时间显示
- 用户认证状态
- 消息状态管理

### 📱 订单管理
- 订单状态筛选
- 订单详情查看
- 订单操作（确认、取消等）

### 👤 个人中心
- 用户信息管理
- 认证状态
- 设置功能

## 快速开始

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
npm install
```

### 开发运行

#### H5 开发
```bash
npm run dev:h5
```

#### 微信小程序开发
```bash
npm run dev:weapp
```

#### 支付宝小程序开发
```bash
npm run dev:alipay
```

### 构建打包

#### H5 构建
```bash
npm run build:h5
```

#### 微信小程序构建
```bash
npm run build:weapp
```

## 设计特色

### 🎨 UI设计
- 采用现代化的卡片式设计
- 渐变色彩搭配，视觉效果佳
- 圆角设计，界面友好
- 响应式布局，适配不同屏幕

### 🚀 用户体验
- 流畅的页面切换动画
- 直观的操作反馈
- 清晰的信息层级
- 便捷的快捷操作

### 📱 移动优先
- 针对移动端优化的交互设计
- 适配不同分辨率设备
- 触摸友好的操作区域
- 优化的加载性能

## 开发说明

### 数据模拟
项目使用模拟数据进行开发，所有API调用都返回模拟数据。在实际部署时，需要：

1. 替换 `src/services/tripApi.ts` 中的API地址
2. 实现真实的后端API接口
3. 配置地图服务（腾讯地图、高德地图等）
4. 集成支付功能
5. 实现实时聊天功能

### 地图集成
项目预留了地图功能接口，需要：
1. 申请地图服务API密钥
2. 配置地图SDK
3. 实现位置选择功能
4. 实现路线规划功能

### 部署建议
1. **H5部署**: 可部署到任何静态文件服务器
2. **小程序部署**: 需要在对应平台注册小程序并配置
3. **App部署**: 需要配置原生开发环境

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件

---

**注意**: 这是一个演示项目，使用了模拟数据。在生产环境中使用前，请确保实现真实的后端API和必要的安全措施。
