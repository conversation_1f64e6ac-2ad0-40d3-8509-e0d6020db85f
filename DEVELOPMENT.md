# 开发指南

## 项目架构

### 技术选型说明

#### Taro 框架
- **优势**: 一套代码多端运行，支持微信小程序、H5、App等
- **版本**: 4.0.9
- **编译器**: Vite（更快的构建速度）

#### TypeScript
- 提供类型安全
- 更好的开发体验
- 减少运行时错误

#### Less 样式预处理器
- 支持嵌套语法
- 变量和混入功能
- 更好的样式组织

### 目录结构详解

```
src/
├── app.config.ts          # 应用全局配置
│   ├── pages             # 页面路由配置
│   ├── window            # 窗口配置
│   ├── tabBar            # 底部导航配置
│   └── permission        # 权限配置
├── app.less              # 全局样式
├── app.ts                # 应用入口文件
├── assets/               # 静态资源目录
│   └── icons/           # 图标资源
├── data/                # 数据层
│   └── mockData.ts      # 模拟数据
├── pages/               # 页面组件
├── services/            # 服务层（API调用）
├── types/               # TypeScript类型定义
├── utils/               # 工具函数
└── components/          # 公共组件（待扩展）
```

## 开发规范

### 代码规范

#### 命名规范
- **文件名**: 使用 kebab-case（如：trip-detail）
- **组件名**: 使用 PascalCase（如：TripDetail）
- **变量名**: 使用 camelCase（如：tripList）
- **常量名**: 使用 UPPER_SNAKE_CASE（如：API_BASE_URL）

#### 组件规范
```typescript
// 组件结构示例
import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import './index.less';

interface ComponentProps {
  // 定义props类型
}

interface ComponentState {
  // 定义state类型
}

const ComponentName: React.FC<ComponentProps> = (props) => {
  const [state, setState] = useState<ComponentState>({
    // 初始状态
  });

  // 生命周期和事件处理函数

  return (
    <View className='component-name'>
      {/* JSX内容 */}
    </View>
  );
};

export default ComponentName;
```

#### 样式规范
```less
.component-name {
  // 组件根样式
  
  .element-class {
    // 元素样式
    
    &.modifier {
      // 修饰符样式
    }
    
    &:hover {
      // 伪类样式
    }
  }
  
  // 响应式样式
  @media (max-width: 375px) {
    // 小屏幕适配
  }
}
```

### API 设计规范

#### 请求封装
```typescript
// services/api.ts
const request = async <T>(url: string, options: RequestOptions): Promise<ApiResponse<T>> => {
  // 统一的请求处理
  // 包含：token处理、错误处理、loading状态等
};
```

#### 响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}
```

### 状态管理

#### 页面状态
使用 React Hooks 进行状态管理：
```typescript
interface PageState {
  loading: boolean;
  data: DataType[];
  error: string | null;
}

const [state, setState] = useState<PageState>({
  loading: false,
  data: [],
  error: null,
});
```

#### 全局状态
对于需要跨页面共享的状态，可以考虑：
1. Context API
2. 状态管理库（如 Zustand、Redux）

## 功能开发指南

### 新增页面

1. **创建页面目录**
```bash
mkdir src/pages/new-page
```

2. **创建页面文件**
```typescript
// src/pages/new-page/index.tsx
import React from 'react';
import { View, Text } from '@tarojs/components';
import './index.less';

const NewPage: React.FC = () => {
  return (
    <View className='new-page'>
      <Text>新页面</Text>
    </View>
  );
};

export default NewPage;
```

3. **创建样式文件**
```less
// src/pages/new-page/index.less
.new-page {
  // 页面样式
}
```

4. **配置路由**
```typescript
// src/app.config.ts
export default defineAppConfig({
  pages: [
    // 其他页面...
    'pages/new-page/index',
  ],
  // 其他配置...
});
```

### 新增API接口

1. **定义类型**
```typescript
// src/types/trip.ts
export interface NewDataType {
  id: string;
  name: string;
  // 其他字段...
}
```

2. **实现API方法**
```typescript
// src/services/tripApi.ts
export const newApi = {
  getData: async (): Promise<ApiResponse<NewDataType[]>> => {
    return mockRequest(mockData);
  },
  
  createData: async (params: CreateParams): Promise<ApiResponse<NewDataType>> => {
    return mockRequest(newData);
  },
};
```

3. **在页面中使用**
```typescript
import { newApi } from '../../services/tripApi';

const loadData = async () => {
  try {
    const response = await newApi.getData();
    if (response.success) {
      // 处理成功响应
    }
  } catch (error) {
    // 处理错误
  }
};
```

## 调试指南

### 开发工具

#### H5 调试
- 使用浏览器开发者工具
- 支持 React DevTools
- 网络请求监控

#### 小程序调试
- 微信开发者工具
- 支持断点调试
- 性能分析工具

### 常见问题

#### 1. 样式不生效
- 检查类名是否正确
- 确认样式文件是否正确导入
- 检查样式优先级

#### 2. API 请求失败
- 检查网络连接
- 确认API地址是否正确
- 查看控制台错误信息

#### 3. 页面跳转问题
- 确认路由配置是否正确
- 检查页面路径是否存在
- 查看跳转参数是否正确

## 性能优化

### 代码优化
1. **懒加载**: 使用动态导入减少初始包大小
2. **代码分割**: 合理拆分组件和页面
3. **避免重复渲染**: 使用 React.memo、useMemo 等

### 资源优化
1. **图片压缩**: 使用适当的图片格式和大小
2. **字体优化**: 只加载需要的字体文件
3. **缓存策略**: 合理设置缓存策略

### 包大小优化
1. **依赖分析**: 定期检查依赖包大小
2. **Tree Shaking**: 确保未使用的代码被移除
3. **按需导入**: 只导入需要的模块

## 测试指南

### 单元测试
```typescript
// 使用 Jest 进行单元测试
describe('Component', () => {
  it('should render correctly', () => {
    // 测试代码
  });
});
```

### 集成测试
- 测试页面间的跳转
- 测试API调用流程
- 测试用户交互流程

### 端到端测试
- 使用自动化测试工具
- 测试完整的用户流程
- 跨平台兼容性测试

## 部署指南

### 环境配置
```typescript
// config/index.ts
const config = {
  development: {
    // 开发环境配置
  },
  production: {
    // 生产环境配置
  },
};
```

### 构建优化
1. **生产构建**: `npm run build:h5`
2. **代码压缩**: 自动启用代码压缩
3. **资源优化**: 自动优化图片和其他资源

### 发布流程
1. 代码审查
2. 测试验证
3. 构建打包
4. 部署上线
5. 监控验证

## 扩展功能

### 地图集成
```typescript
// 集成腾讯地图或高德地图
import { Map } from '@tarojs/components';

const MapComponent = () => {
  return (
    <Map
      latitude={39.908823}
      longitude={116.397470}
      // 其他配置...
    />
  );
};
```

### 支付集成
```typescript
// 集成微信支付或支付宝
const handlePayment = async (orderInfo) => {
  try {
    const result = await Taro.requestPayment({
      // 支付参数
    });
    // 处理支付结果
  } catch (error) {
    // 处理支付错误
  }
};
```

### 推送通知
```typescript
// 集成消息推送
const subscribePush = async () => {
  try {
    const result = await Taro.requestSubscribeMessage({
      tmplIds: ['template_id'],
    });
    // 处理订阅结果
  } catch (error) {
    // 处理订阅错误
  }
};
```

## 最佳实践

1. **组件复用**: 提取公共组件，避免重复代码
2. **类型安全**: 充分利用 TypeScript 的类型系统
3. **错误处理**: 统一的错误处理机制
4. **用户体验**: 合理的加载状态和错误提示
5. **性能监控**: 定期检查应用性能指标
6. **代码审查**: 建立代码审查流程
7. **文档维护**: 及时更新开发文档
