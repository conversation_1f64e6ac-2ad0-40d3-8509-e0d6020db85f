import React, { useState, useEffect } from 'react';
import { View, Text, Map } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { getCurrentLocation, chooseLocation, showToast } from '../../utils/common';
import { Location, MapMarker } from '../../types/trip';
import './index.less';

interface HomePageState {
  currentLocation: Location | null;
  startLocation: Location | null;
  endLocation: Location | null;
  mapCenter: {
    latitude: number;
    longitude: number;
  };
  markers: MapMarker[];
  showSearch: boolean;
  searchKeyword: string;
  activeTab: 'carpool' | 'taxi' | 'special';
  userInfo: {
    name: string;
    avatar: string;
    location: string;
    temperature: string;
  };
}

const HomePage: React.FC = () => {
  const [state, setState] = useState<HomePageState>({
    currentLocation: null,
    startLocation: null,
    endLocation: null,
    mapCenter: {
      latitude: 28.15,
      longitude: 113.63,
    },
    markers: [],
    showSearch: false,
    searchKeyword: '',
    activeTab: 'carpool',
    userInfo: {
      name: '龙岗区',
      avatar: '',
      location: '晴',
      temperature: '19°C',
    },
  });



  useDidShow(() => {
    initLocation();
  });

  // 初始化位置
  const initLocation = async () => {
    try {
      const location = await getCurrentLocation();
      const currentLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: '当前位置',
      };

      setState(prev => ({
        ...prev,
        currentLocation,
        mapCenter: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
        markers: [{
          id: 'current',
          latitude: location.latitude,
          longitude: location.longitude,
          iconPath: '/assets/icons/current-location.png',
          width: 30,
          height: 30,
          title: '当前位置',
        }],
      }));
    } catch (error) {
      console.error('获取位置失败:', error);
      showToast('获取位置失败，请检查定位权限');
    }
  };

  // 选择终点
  const selectEndLocation = async () => {
    try {
      const location = await chooseLocation();
      const endLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
        name: location.name,
      };

      setState(prev => ({
        ...prev,
        endLocation,
      }));
    } catch (error) {
      console.error('选择终点失败:', error);
    }
  };



  // 地图点击事件
  const onMapTap = (e: any) => {
    console.log('地图点击:', e);
  };

  // 标记点击事件
  const onMarkerTap = (e: any) => {
    console.log('标记点击:', e);
  };



  return (
    <View className='home-page'>
      {/* 地图 */}
      <Map
        className='map'
        latitude={state.mapCenter.latitude}
        longitude={state.mapCenter.longitude}
        scale={16}
        markers={state.markers.map(marker => ({
          ...marker,
          id: parseInt(marker.id)
        }))}
        showLocation
        showScale
        showCompass
        enableOverlooking
        enableZoom
        enableScroll
        enableRotate
        onTap={onMapTap}
        onMarkerTap={onMarkerTap}
        onError={(e) => console.error('地图错误:', e)}
      />

      {/* 顶部状态栏 */}
      <View className='top-status-bar'>
        <Text className='app-title'>好好出行</Text>
        <View className='status-actions'>
          <View className='menu-btn'>
            <Text className='menu-icon'>⋯</Text>
          </View>
          <View className='location-btn'>
            <Text className='location-icon'>⊙</Text>
          </View>
        </View>
      </View>

      {/* 当前位置提示 */}
      <View className='current-location-tip'>
        <View className='location-badge'>
          <Text className='badge-text'>在这里上车</Text>
        </View>
        <View className='location-info'>
          <Text className='location-name'>北三环·渤海北金·B座</Text>
          <View className='location-arrow'>
            <Text className='arrow-icon'>↓</Text>
          </View>
        </View>
      </View>

      {/* 快车出行卡片 */}
      <View className='ride-card'>
        <View className='card-header'>
          <View className='service-icon'>
            <Text className='icon'>�</Text>
          </View>
          <Text className='service-title'>快车出行</Text>
        </View>

        <View className='route-section'>
          <View className='route-item start'>
            <View className='route-dot green'></View>
            <Text className='route-text'>北三环·渤海北金·B座 上车 {'>'}</Text>
          </View>

          <View className='route-item destination' onClick={selectEndLocation}>
            <View className='route-dot red'></View>
            <Text className='route-text destination-text'>输入您的目的地</Text>
          </View>
        </View>

        <View className='booking-options'>
          <View className='booking-item'>
            <Text className='booking-text'>预约快车</Text>
          </View>
          <View className='booking-item'>
            <Text className='booking-text'>代叫快车</Text>
          </View>
        </View>
      </View>

      {/* 地图控制按钮 */}
      <View className='map-controls'>
        <View className='control-btn location-btn' onClick={initLocation}>
          <Text className='control-icon'>⊙</Text>
        </View>
      </View>

      {/* 底部导航栏 */}
      <View className='bottom-nav'>
        <View className='nav-item active'>
          <Text className='nav-icon'>🚗</Text>
          <Text className='nav-text'>快车</Text>
        </View>
        <View className='nav-item'>
          <Text className='nav-icon'>🚌</Text>
          <Text className='nav-text'>城际</Text>
        </View>
        <View className='nav-item'>
          <Text className='nav-icon'>👤</Text>
          <Text className='nav-text'>代驾</Text>
        </View>
        <View className='nav-item'>
          <Text className='nav-icon'>👨</Text>
          <Text className='nav-text'>我的</Text>
        </View>
      </View>
    </View>
  );
};

export default HomePage;
