import React, { useState, useEffect } from 'react';
import { View, Text, Map, Input, Image, ScrollView } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { getCurrentLocation, chooseLocation, showToast, formatTime } from '../../utils/common';
import { Location, MapMarker, QuickService, Activity } from '../../types/trip';
import './index.less';

interface HomePageState {
  currentLocation: Location | null;
  startLocation: Location | null;
  endLocation: Location | null;
  mapCenter: {
    latitude: number;
    longitude: number;
  };
  markers: MapMarker[];
  showSearch: boolean;
  searchKeyword: string;
  activeTab: 'carpool' | 'taxi' | 'special';
  userInfo: {
    name: string;
    avatar: string;
    location: string;
    temperature: string;
  };
}

const HomePage: React.FC = () => {
  const [state, setState] = useState<HomePageState>({
    currentLocation: null,
    startLocation: null,
    endLocation: null,
    mapCenter: {
      latitude: 39.908823,
      longitude: 116.397470,
    },
    markers: [],
    showSearch: false,
    searchKeyword: '',
    activeTab: 'carpool',
    userInfo: {
      name: '龙岗区',
      avatar: '',
      location: '晴',
      temperature: '19°C',
    },
  });

  // 快捷服务数据
  const quickServices: QuickService[] = [
    { id: '1', name: '打车', icon: '🚗', description: '快速叫车' },
    { id: '2', name: '找约车', icon: '🚙', description: '预约出行' },
    { id: '3', name: '顺风车', icon: '🚘', description: '经济出行' },
    { id: '4', name: '城际专车', icon: '🚌', description: '长途出行' },
    { id: '5', name: '租车', icon: '🚕', description: '自驾出行' },
    { id: '6', name: '送货搬家', icon: '🚚', description: '货运服务' },
    { id: '7', name: '代驾', icon: '🚗', description: '安全代驾' },
    { id: '8', name: '特惠充电', icon: '🔋', description: '充电服务' },
    { id: '9', name: '成为车主', icon: '👤', description: '加入我们' },
    { id: '10', name: '红包车', icon: '🎁', description: '优惠出行' },
  ];

  // 活动数据
  const activities: Activity[] = [
    {
      id: '1',
      title: '这好友 返现金',
      description: '每日签到',
      image: '',
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      isActive: true,
    },
  ];

  useDidShow(() => {
    initLocation();
  });

  // 初始化位置
  const initLocation = async () => {
    try {
      const location = await getCurrentLocation();
      const currentLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: '当前位置',
      };

      setState(prev => ({
        ...prev,
        currentLocation,
        mapCenter: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
        markers: [{
          id: 'current',
          latitude: location.latitude,
          longitude: location.longitude,
          iconPath: '/assets/icons/current-location.png',
          width: 30,
          height: 30,
          title: '当前位置',
        }],
      }));
    } catch (error) {
      console.error('获取位置失败:', error);
      showToast('获取位置失败，请检查定位权限');
    }
  };

  // 选择起点
  const selectStartLocation = async () => {
    try {
      const location = await chooseLocation();
      const startLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
        name: location.name,
      };

      setState(prev => ({
        ...prev,
        startLocation,
      }));
    } catch (error) {
      console.error('选择起点失败:', error);
    }
  };

  // 选择终点
  const selectEndLocation = async () => {
    try {
      const location = await chooseLocation();
      const endLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
        name: location.name,
      };

      setState(prev => ({
        ...prev,
        endLocation,
      }));
    } catch (error) {
      console.error('选择终点失败:', error);
    }
  };

  // 搜索行程
  const searchTrips = () => {
    if (!state.startLocation || !state.endLocation) {
      showToast('请选择起点和终点');
      return;
    }

    Taro.navigateTo({
      url: `/pages/search-result/index?start=${encodeURIComponent(JSON.stringify(state.startLocation))}&end=${encodeURIComponent(JSON.stringify(state.endLocation))}`,
    });
  };

  // 发布行程
  const publishTrip = () => {
    Taro.navigateTo({
      url: '/pages/publish/index',
    });
  };

  // 地图点击事件
  const onMapTap = (e: any) => {
    console.log('地图点击:', e);
  };

  // 标记点击事件
  const onMarkerTap = (e: any) => {
    console.log('标记点击:', e);
  };

  // 处理服务点击
  const handleServiceClick = (service: QuickService) => {
    switch (service.name) {
      case '打车':
        showToast('打车功能开发中');
        break;
      case '顺风车':
        Taro.navigateTo({ url: '/pages/publish/index' });
        break;
      case '城际专车':
        showToast('城际专车功能开发中');
        break;
      case '租车':
        showToast('租车功能开发中');
        break;
      case '代驾':
        showToast('代驾功能开发中');
        break;
      case '成为车主':
        Taro.navigateTo({ url: '/pages/profile/index' });
        break;
      default:
        showToast(`${service.name}功能开发中`);
    }
  };

  return (
    <View className='home-page'>
      {/* 地图 */}
      <Map
        className='map'
        latitude={state.mapCenter.latitude}
        longitude={state.mapCenter.longitude}
        scale={16}
        markers={state.markers}
        showLocation
        showScale
        showCompass
        enableOverlooking
        enableZoom
        enableScroll
        enableRotate
        onTap={onMapTap}
        onMarkerTap={onMarkerTap}
      />

      {/* 顶部用户信息栏 */}
      <View className='top-header'>
        <View className='user-info'>
          <Image className='user-avatar' src={state.userInfo.avatar || '/assets/icons/default-avatar.png'} />
          <View className='user-details'>
            <Text className='user-name'>{state.userInfo.name}</Text>
            <Text className='user-location'>{state.userInfo.temperature} {state.userInfo.location}</Text>
          </View>
        </View>
        <View className='header-actions'>
          <View className='notification-btn'>
            <Text className='notification-badge'>1</Text>
            <Text className='notification-icon'>🔔</Text>
          </View>
          <View className='settings-btn'>
            <Text className='settings-icon'>⚙️</Text>
          </View>
        </View>
      </View>

      {/* 出行方式选择 */}
      <View className='travel-tabs'>
        <View
          className={`tab-item ${state.activeTab === 'carpool' ? 'active' : ''}`}
          onClick={() => setState(prev => ({ ...prev, activeTab: 'carpool' }))}
        >
          <Text className='tab-text'>顺风车</Text>
        </View>
        <View
          className={`tab-item ${state.activeTab === 'taxi' ? 'active' : ''}`}
          onClick={() => setState(prev => ({ ...prev, activeTab: 'taxi' }))}
        >
          <Text className='tab-text'>打车</Text>
        </View>
        <View
          className={`tab-item ${state.activeTab === 'special' ? 'active' : ''}`}
          onClick={() => setState(prev => ({ ...prev, activeTab: 'special' }))}
        >
          <Text className='tab-text'>城际专车</Text>
        </View>
      </View>

      {/* 地点选择区域 */}
      <View className='location-selector'>
        <View className='current-trip'>
          <View className='trip-info'>
            <Text className='trip-icon'>🔵</Text>
            <Text className='trip-text'>万象大厦-西北门 (上午8点)</Text>
          </View>
        </View>

        <View className='destination-input' onClick={selectEndLocation}>
          <Text className='destination-placeholder'>您要去哪儿?</Text>
        </View>
      </View>

      {/* 快捷服务网格 */}
      <View className='services-grid'>
        {quickServices.map((service, index) => (
          <View key={service.id} className='service-item' onClick={() => handleServiceClick(service)}>
            <Text className='service-icon'>{service.icon}</Text>
            <Text className='service-name'>{service.name}</Text>
          </View>
        ))}
      </View>

      {/* 福利活动 */}
      <View className='welfare-section'>
        <Text className='section-title'>福利活动</Text>
        <ScrollView scrollX className='activity-scroll'>
          {activities.map((activity) => (
            <View key={activity.id} className='activity-card'>
              <View className='activity-content'>
                <Text className='activity-title'>{activity.title}</Text>
                <Text className='activity-subtitle'>{activity.description}</Text>
                <View className='activity-reward'>
                  <Text className='reward-amount'>30</Text>
                  <Text className='reward-unit'>元</Text>
                </View>
              </View>
              <View className='activity-action'>
                <Text className='action-btn'>立即参与活动</Text>
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

export default HomePage;
