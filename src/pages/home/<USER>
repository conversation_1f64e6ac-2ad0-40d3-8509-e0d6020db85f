.home-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;

  .map {
    width: 100%;
    height: 100vh;
  }

  // 顶部状态栏
  .top-status-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    padding: 44px 16px 16px;
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .app-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .status-actions {
      display: flex;
      gap: 12px;

      .menu-btn,
      .location-btn {
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .menu-icon,
        .location-icon {
          font-size: 16px;
          color: #333;
        }
      }
    }
  }

  // 当前位置提示
  .current-location-tip {
    position: absolute;
    top: 120px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;

    .location-badge {
      background: #4CAF50;
      color: white;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      margin-bottom: 8px;

      .badge-text {
        color: white;
      }
    }

    .location-info {
      background: white;
      padding: 12px 16px;
      border-radius: 20px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 8px;

      .location-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .location-arrow {
        .arrow-icon {
          font-size: 16px;
          color: #4CAF50;
        }
      }
    }
  }

  // 快车出行卡片
  .ride-card {
    position: absolute;
    bottom: 100px;
    left: 16px;
    right: 16px;
    z-index: 10;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 2px solid #FF6B35;

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .service-icon {
        width: 32px;
        height: 32px;
        background: #FF6B35;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        .icon {
          font-size: 18px;
          color: white;
        }
      }

      .service-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .route-section {
      margin-bottom: 16px;

      .route-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .route-dot {
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-right: 12px;
          flex-shrink: 0;

          &.green {
            background: #4CAF50;
          }

          &.red {
            background: #FF4757;
          }
        }

        .route-text {
          font-size: 14px;
          color: #333;

          &.destination-text {
            color: #999;
          }
        }

        &.destination {
          cursor: pointer;
        }
      }
    }

    .booking-options {
      display: flex;
      gap: 12px;

      .booking-item {
        flex: 1;
        padding: 12px;
        background: #f8f8f8;
        border-radius: 8px;
        text-align: center;

        .booking-text {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  // 地图控制按钮
  .map-controls {
    position: absolute;
    bottom: 240px;
    right: 16px;
    z-index: 10;

    .control-btn {
      width: 44px;
      height: 44px;
      background: white;
      border-radius: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .control-icon {
        font-size: 20px;
        color: #333;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // 底部导航栏
  .bottom-nav {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: white;
    padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
    border-top: 1px solid #f0f0f0;
    display: flex;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;

      &.active {
        .nav-icon {
          color: #FF6B35;
        }

        .nav-text {
          color: #FF6B35;
        }
      }

      .nav-icon {
        font-size: 20px;
        margin-bottom: 4px;
        color: #999;
      }

      .nav-text {
        font-size: 12px;
        color: #999;
      }
    }
  }
}


