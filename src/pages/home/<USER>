import React, { useState, useEffect } from 'react';
import { View, Text, Map, Input } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { getCurrentLocation, chooseLocation, showToast } from '../../utils/common';
import { Location, MapMarker } from '../../types/trip';
import './index.less';

interface HomePageState {
  currentLocation: Location | null;
  startLocation: Location | null;
  endLocation: Location | null;
  mapCenter: {
    latitude: number;
    longitude: number;
  };
  markers: MapMarker[];
  showSearch: boolean;
  searchKeyword: string;
}

const HomePage: React.FC = () => {
  const [state, setState] = useState<HomePageState>({
    currentLocation: null,
    startLocation: null,
    endLocation: null,
    mapCenter: {
      latitude: 39.908823,
      longitude: 116.397470,
    },
    markers: [],
    showSearch: false,
    searchKeyword: '',
  });

  useDidShow(() => {
    initLocation();
  });

  // 初始化位置
  const initLocation = async () => {
    try {
      const location = await getCurrentLocation();
      const currentLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: '当前位置',
      };

      setState(prev => ({
        ...prev,
        currentLocation,
        mapCenter: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
        markers: [{
          id: 'current',
          latitude: location.latitude,
          longitude: location.longitude,
          iconPath: '/assets/icons/current-location.png',
          width: 30,
          height: 30,
          title: '当前位置',
        }],
      }));
    } catch (error) {
      console.error('获取位置失败:', error);
      showToast('获取位置失败，请检查定位权限');
    }
  };

  // 选择起点
  const selectStartLocation = async () => {
    try {
      const location = await chooseLocation();
      const startLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
        name: location.name,
      };

      setState(prev => ({
        ...prev,
        startLocation,
      }));
    } catch (error) {
      console.error('选择起点失败:', error);
    }
  };

  // 选择终点
  const selectEndLocation = async () => {
    try {
      const location = await chooseLocation();
      const endLocation: Location = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
        name: location.name,
      };

      setState(prev => ({
        ...prev,
        endLocation,
      }));
    } catch (error) {
      console.error('选择终点失败:', error);
    }
  };

  // 搜索行程
  const searchTrips = () => {
    if (!state.startLocation || !state.endLocation) {
      showToast('请选择起点和终点');
      return;
    }

    Taro.navigateTo({
      url: `/pages/search-result/index?start=${encodeURIComponent(JSON.stringify(state.startLocation))}&end=${encodeURIComponent(JSON.stringify(state.endLocation))}`,
    });
  };

  // 发布行程
  const publishTrip = () => {
    Taro.navigateTo({
      url: '/pages/publish/index',
    });
  };

  // 地图点击事件
  const onMapTap = (e: any) => {
    console.log('地图点击:', e);
  };

  // 标记点击事件
  const onMarkerTap = (e: any) => {
    console.log('标记点击:', e);
  };

  return (
    <View className='home-page'>
      {/* 地图 */}
      <Map
        className='map'
        latitude={state.mapCenter.latitude}
        longitude={state.mapCenter.longitude}
        scale={16}
        markers={state.markers}
        showLocation
        showScale
        showCompass
        enableOverlooking
        enableZoom
        enableScroll
        enableRotate
        onTap={onMapTap}
        onMarkerTap={onMarkerTap}
      />

      {/* 顶部搜索栏 */}
      <View className='top-bar'>
        <View className='search-container'>
          <View className='location-inputs'>
            <View className='location-item' onClick={selectStartLocation}>
              <View className='location-dot start-dot' />
              <Text className='location-text'>
                {state.startLocation?.name || state.startLocation?.address || '请选择起点'}
              </Text>
            </View>
            
            <View className='location-divider' />
            
            <View className='location-item' onClick={selectEndLocation}>
              <View className='location-dot end-dot' />
              <Text className='location-text'>
                {state.endLocation?.name || state.endLocation?.address || '请选择终点'}
              </Text>
            </View>
          </View>
          
          <View className='search-btn' onClick={searchTrips}>
            搜索
          </View>
        </View>
      </View>

      {/* 底部操作区 */}
      <View className='bottom-panel'>
        {/* 快捷地址 */}
        <View className='quick-addresses'>
          <View className='quick-item'>
            <View className='quick-icon home-icon' />
            <Text className='quick-text'>家</Text>
            <Text className='quick-desc'>设置家的地址</Text>
          </View>
          
          <View className='quick-item'>
            <View className='quick-icon company-icon' />
            <Text className='quick-text'>公司</Text>
            <Text className='quick-desc'>设置公司的地址</Text>
          </View>
        </View>

        {/* 功能按钮 */}
        <View className='action-buttons'>
          <View className='action-btn primary' onClick={publishTrip}>
            <Text className='btn-text'>发布行程</Text>
          </View>
          
          <View className='action-btn secondary' onClick={() => Taro.navigateTo({ url: '/pages/order/index' })}>
            <Text className='btn-text'>我的订单</Text>
          </View>
        </View>

        {/* 安全中心 */}
        <View className='safety-center' onClick={() => Taro.navigateTo({ url: '/pages/safety/index' })}>
          <View className='safety-icon' />
          <Text className='safety-text'>安全中心</Text>
        </View>
      </View>
    </View>
  );
};

export default HomePage;
