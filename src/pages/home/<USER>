.home-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;

  .map {
    width: 100%;
    height: 100%;
  }

  .top-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 20px 16px 16px;
    background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.8) 100%);

    .search-container {
      display: flex;
      align-items: center;
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);

      .location-inputs {
        flex: 1;
        margin-right: 12px;

        .location-item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          min-height: 24px;

          .location-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;

            &.start-dot {
              background: #52c41a;
            }

            &.end-dot {
              background: #ff4d4f;
            }
          }

          .location-text {
            flex: 1;
            font-size: 14px;
            color: #333;
            line-height: 1.4;

            &:empty::before {
              content: attr(placeholder);
              color: #999;
            }
          }
        }

        .location-divider {
          width: 1px;
          height: 16px;
          background: #e8e8e8;
          margin: 4px 0 4px 16px;
        }
      }

      .search-btn {
        background: #007AFF;
        color: #fff;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
      }
    }
  }

  .bottom-panel {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: #fff;
    border-radius: 20px 20px 0 0;
    padding: 20px 16px 40px;
    box-shadow: 0 -2px 12px rgba(0,0,0,0.1);

    .quick-addresses {
      display: flex;
      margin-bottom: 20px;
      gap: 12px;

      .quick-item {
        flex: 1;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 16px;
        display: flex;
        align-items: center;
        position: relative;

        .quick-icon {
          width: 24px;
          height: 24px;
          margin-right: 12px;
          border-radius: 4px;

          &.home-icon {
            background: #52c41a;
            position: relative;

            &::after {
              content: '🏠';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 12px;
            }
          }

          &.company-icon {
            background: #1890ff;
            position: relative;

            &::after {
              content: '🏢';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 12px;
            }
          }
        }

        .quick-text {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 2px;
        }

        .quick-desc {
          font-size: 12px;
          color: #999;
          position: absolute;
          bottom: 8px;
          left: 48px;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;

      .action-btn {
        flex: 1;
        padding: 16px;
        border-radius: 12px;
        text-align: center;

        &.primary {
          background: #007AFF;

          .btn-text {
            color: #fff;
            font-size: 16px;
            font-weight: 500;
          }
        }

        &.secondary {
          background: #f8f9fa;
          border: 1px solid #e8e8e8;

          .btn-text {
            color: #333;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }

    .safety-center {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 8px;
      border: 1px solid #e6f7ff;

      .safety-icon {
        width: 20px;
        height: 20px;
        background: #1890ff;
        border-radius: 50%;
        margin-right: 8px;
        position: relative;

        &::after {
          content: '🛡️';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
        }
      }

      .safety-text {
        font-size: 14px;
        color: #1890ff;
        font-weight: 500;
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .home-page {
    .top-bar {
      padding: 16px 12px 12px;

      .search-container {
        padding: 12px;

        .location-inputs {
          .location-item {
            padding: 6px 0;

            .location-text {
              font-size: 13px;
            }
          }
        }

        .search-btn {
          padding: 10px 16px;
          font-size: 13px;
        }
      }
    }

    .bottom-panel {
      padding: 16px 12px 36px;

      .quick-addresses {
        .quick-item {
          padding: 12px;

          .quick-text {
            font-size: 15px;
          }

          .quick-desc {
            font-size: 11px;
            left: 40px;
          }
        }
      }

      .action-buttons {
        .action-btn {
          padding: 14px;

          .btn-text {
            font-size: 15px;
          }
        }
      }
    }
  }
}
