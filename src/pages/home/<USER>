.home-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;

  .map {
    width: 100%;
    height: 60%;
  }

  // 顶部用户信息栏
  .top-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 44px 16px 16px;
    background: linear-gradient(180deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.8) 100%);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .user-info {
      display: flex;
      align-items: center;

      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        background: #e8e8e8;
      }

      .user-details {
        .user-name {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          display: block;
          margin-bottom: 2px;
        }

        .user-location {
          font-size: 12px;
          color: #666;
          display: block;
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .notification-btn {
        position: relative;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        .notification-badge {
          position: absolute;
          top: -2px;
          right: -2px;
          background: #ff4d4f;
          color: #fff;
          font-size: 10px;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .notification-icon {
          font-size: 18px;
        }
      }

      .settings-btn {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        .settings-icon {
          font-size: 18px;
        }
      }
    }
  }

  // 出行方式选择
  .travel-tabs {
    position: absolute;
    top: 120px;
    left: 16px;
    right: 16px;
    z-index: 10;
    background: #fff;
    border-radius: 12px;
    padding: 4px;
    display: flex;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);

    .tab-item {
      flex: 1;
      padding: 12px;
      text-align: center;
      border-radius: 8px;
      transition: all 0.3s;

      &.active {
        background: #007AFF;

        .tab-text {
          color: #fff;
          font-weight: 500;
        }
      }

      .tab-text {
        font-size: 14px;
        color: #666;
      }
    }
  }

  // 地点选择区域
  .location-selector {
    position: absolute;
    top: 180px;
    left: 16px;
    right: 16px;
    z-index: 10;

    .current-trip {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);

      .trip-info {
        display: flex;
        align-items: center;

        .trip-icon {
          margin-right: 12px;
          font-size: 16px;
        }

        .trip-text {
          font-size: 14px;
          color: #333;
        }
      }
    }

    .destination-input {
      background: #fff;
      border-radius: 12px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.1);
      border: 2px solid #ff6b35;

      .destination-placeholder {
        font-size: 16px;
        color: #333;
      }
    }
  }

  // 快捷服务网格
  .services-grid {
    position: absolute;
    bottom: 120px;
    left: 16px;
    right: 16px;
    z-index: 10;
    background: #fff;
    border-radius: 16px;
    padding: 20px 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px 16px;

    .service-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 8px 4px;

      .service-icon {
        font-size: 24px;
        margin-bottom: 8px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 12px;
      }

      .service-name {
        font-size: 12px;
        color: #333;
        line-height: 1.2;
      }
    }
  }

  // 福利活动
  .welfare-section {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 16px;
    z-index: 10;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
      display: block;
    }

    .activity-scroll {
      white-space: nowrap;

      .activity-card {
        display: inline-block;
        width: 280px;
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 16px;
        padding: 20px;
        margin-right: 12px;
        position: relative;
        overflow: hidden;

        .activity-content {
          .activity-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 4px;
            display: block;
          }

          .activity-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 16px;
            display: block;
          }

          .activity-reward {
            display: flex;
            align-items: baseline;
            margin-bottom: 16px;

            .reward-amount {
              font-size: 32px;
              font-weight: 700;
              color: #fff;
            }

            .reward-unit {
              font-size: 16px;
              color: #fff;
              margin-left: 4px;
            }
          }
        }

        .activity-action {
          .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            display: inline-block;
          }
        }
      }
    }
  }


}

/* 响应式适配 */
@media (max-width: 375px) {
  .home-page {
    .top-header {
      padding: 40px 12px 12px;

      .user-info {
        .user-avatar {
          width: 36px;
          height: 36px;
        }

        .user-details {
          .user-name {
            font-size: 15px;
          }

          .user-location {
            font-size: 11px;
          }
        }
      }
    }

    .travel-tabs {
      left: 12px;
      right: 12px;

      .tab-item {
        padding: 10px;

        .tab-text {
          font-size: 13px;
        }
      }
    }

    .location-selector {
      left: 12px;
      right: 12px;
    }

    .services-grid {
      left: 12px;
      right: 12px;
      padding: 16px 12px;
      gap: 16px 12px;

      .service-item {
        .service-icon {
          font-size: 20px;
          width: 36px;
          height: 36px;
        }

        .service-name {
          font-size: 11px;
        }
      }
    }

    .welfare-section {
      left: 12px;
      right: 12px;

      .activity-card {
        width: 260px;
        padding: 16px;
      }
    }
  }
}
