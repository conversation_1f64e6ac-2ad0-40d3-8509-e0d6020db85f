.search-result-page {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  .header {
    background: #fff;
    padding: 44px 16px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 10;

    .back-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .back-icon {
        font-size: 20px;
        color: #333;
      }
    }

    .route-info {
      flex: 1;
      text-align: center;

      .route-text {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .filter-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      .filter-icon {
        font-size: 18px;
        color: #666;
      }
    }
  }

  .trip-list {
    flex: 1;
    padding: 16px;

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;

      .loading-text {
        font-size: 16px;
        color: #666;
      }
    }

    .trip-item {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 12px rgba(0,0,0,0.08);

      .driver-info {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .driver-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 12px;
          background: #e8e8e8;
        }

        .driver-details {
          flex: 1;

          .driver-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            display: block;
          }

          .driver-rating {
            display: flex;
            align-items: center;

            .rating-text {
              font-size: 12px;
              color: #ff9500;
              margin-right: 8px;
            }

            .review-count {
              font-size: 12px;
              color: #999;
            }
          }
        }

        .trip-price {
          text-align: right;

          .price-text {
            font-size: 20px;
            font-weight: 600;
            color: #ff6b35;
          }

          .price-unit {
            font-size: 12px;
            color: #999;
          }
        }
      }

      .trip-info {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .time-info {
          width: 60px;
          text-align: center;

          .departure-time {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 2px;
          }

          .date-text {
            font-size: 12px;
            color: #999;
            display: block;
          }
        }

        .route-info {
          flex: 1;
          margin: 0 16px;
          position: relative;

          .route-line {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .start-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #52c41a;
              flex-shrink: 0;
            }

            .route-dash {
              flex: 1;
              height: 1px;
              background: linear-gradient(to right, #52c41a 0%, #ff4d4f 100%);
              margin: 0 8px;
              position: relative;

              &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-left: 4px solid #ff4d4f;
                border-top: 2px solid transparent;
                border-bottom: 2px solid transparent;
              }
            }

            .end-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #ff4d4f;
              flex-shrink: 0;
            }
          }

          .route-text {
            display: flex;
            justify-content: space-between;

            .start-location,
            .end-location {
              font-size: 12px;
              color: #666;
              max-width: 45%;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .seats-info {
          width: 40px;
          text-align: center;

          .seats-text {
            font-size: 14px;
            color: #007AFF;
            font-weight: 500;
          }
        }
      }

      .car-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 12px;

        .car-text {
          font-size: 14px;
          color: #666;
        }

        .plate-number {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }

      .requirements {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .requirement-tag {
          background: #e6f7ff;
          color: #1890ff;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 12px;
          border: 1px solid #91d5ff;
        }
      }
    }

    .empty-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      text-align: center;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
        display: block;
      }

      .empty-desc {
        font-size: 14px;
        color: #999;
        display: block;
      }
    }
  }

  .bottom-actions {
    background: #fff;
    padding: 16px;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);

    .publish-btn {
      background: #007AFF;
      color: #fff;
      padding: 16px;
      border-radius: 12px;
      text-align: center;

      .publish-text {
        font-size: 16px;
        font-weight: 500;
        color: #fff;
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .search-result-page {
    .header {
      padding: 40px 12px 12px;

      .route-text {
        font-size: 15px;
      }
    }

    .trip-list {
      padding: 12px;

      .trip-item {
        padding: 16px;

        .driver-info {
          .driver-avatar {
            width: 44px;
            height: 44px;
          }

          .driver-details {
            .driver-name {
              font-size: 15px;
            }
          }

          .trip-price {
            .price-text {
              font-size: 18px;
            }
          }
        }

        .trip-info {
          .time-info {
            width: 55px;

            .departure-time {
              font-size: 16px;
            }
          }
        }
      }
    }

    .bottom-actions {
      padding: 12px;

      .publish-btn {
        padding: 14px;

        .publish-text {
          font-size: 15px;
        }
      }
    }
  }
}
