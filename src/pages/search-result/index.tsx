import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Image } from '@tarojs/components';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { tripApi } from '../../services/tripApi';
import { formatTime, formatPrice, showToast, showLoading, hideLoading } from '../../utils/common';
import { Trip, Location, SearchParams } from '../../types/trip';
import './index.less';

interface SearchResultState {
  trips: Trip[];
  loading: boolean;
  searchParams: SearchParams | null;
}

const SearchResultPage: React.FC = () => {
  const router = useRouter();
  const [state, setState] = useState<SearchResultState>({
    trips: [],
    loading: false,
    searchParams: null,
  });

  useDidShow(() => {
    const { start, end } = router.params;
    if (start && end) {
      try {
        const startLocation: Location = JSON.parse(decodeURIComponent(start));
        const endLocation: Location = JSON.parse(decodeURIComponent(end));
        const searchParams: SearchParams = {
          startLocation,
          endLocation,
        };
        setState(prev => ({ ...prev, searchParams }));
        searchTrips(searchParams);
      } catch (error) {
        console.error('解析搜索参数失败:', error);
        showToast('搜索参数错误');
      }
    }
  });

  // 搜索行程
  const searchTrips = async (params: SearchParams) => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      showLoading('搜索中...');

      const response = await tripApi.searchTrips(params);
      
      if (response.success) {
        setState(prev => ({ 
          ...prev, 
          trips: response.data || [],
          loading: false 
        }));
      } else {
        throw new Error(response.message || '搜索失败');
      }
    } catch (error: any) {
      console.error('搜索行程失败:', error);
      showToast(error.message || '搜索失败');
      setState(prev => ({ ...prev, loading: false }));
    } finally {
      hideLoading();
    }
  };

  // 预订行程
  const bookTrip = (trip: Trip) => {
    Taro.navigateTo({
      url: `/pages/trip-detail/index?id=${trip.id}`,
    });
  };

  // 返回上一页
  const goBack = () => {
    Taro.navigateBack();
  };

  return (
    <View className='search-result-page'>
      {/* 顶部导航 */}
      <View className='header'>
        <View className='back-btn' onClick={goBack}>
          <Text className='back-icon'>←</Text>
        </View>
        <View className='route-info'>
          <Text className='route-text'>
            {state.searchParams?.startLocation.name || state.searchParams?.startLocation.address} → {state.searchParams?.endLocation.name || state.searchParams?.endLocation.address}
          </Text>
        </View>
        <View className='filter-btn'>
          <Text className='filter-icon'>⚙️</Text>
        </View>
      </View>

      {/* 搜索结果列表 */}
      <ScrollView className='trip-list' scrollY>
        {state.loading ? (
          <View className='loading-container'>
            <Text className='loading-text'>搜索中...</Text>
          </View>
        ) : state.trips.length > 0 ? (
          state.trips.map((trip) => (
            <View key={trip.id} className='trip-item' onClick={() => bookTrip(trip)}>
              {/* 司机信息 */}
              <View className='driver-info'>
                <Image 
                  className='driver-avatar' 
                  src={trip.driver.avatar || '/assets/icons/default-avatar.png'} 
                />
                <View className='driver-details'>
                  <Text className='driver-name'>{trip.driver.name}</Text>
                  <View className='driver-rating'>
                    <Text className='rating-text'>⭐ {trip.driver.rating || 5.0}</Text>
                    <Text className='review-count'>({trip.driver.reviewCount || 0}条评价)</Text>
                  </View>
                </View>
                <View className='trip-price'>
                  <Text className='price-text'>{formatPrice(trip.price)}</Text>
                  <Text className='price-unit'>/人</Text>
                </View>
              </View>

              {/* 行程信息 */}
              <View className='trip-info'>
                <View className='time-info'>
                  <Text className='departure-time'>{formatTime(trip.departureTime, 'HH:mm')}</Text>
                  <Text className='date-text'>{formatTime(trip.departureTime, 'MM-DD')}</Text>
                </View>
                
                <View className='route-info'>
                  <View className='route-line'>
                    <View className='start-dot' />
                    <View className='route-dash' />
                    <View className='end-dot' />
                  </View>
                  <View className='route-text'>
                    <Text className='start-location'>{trip.startLocation.name || trip.startLocation.address}</Text>
                    <Text className='end-location'>{trip.endLocation.name || trip.endLocation.address}</Text>
                  </View>
                </View>

                <View className='seats-info'>
                  <Text className='seats-text'>{trip.availableSeats}座</Text>
                </View>
              </View>

              {/* 车辆信息 */}
              {trip.driver.carInfo && (
                <View className='car-info'>
                  <Text className='car-text'>
                    {trip.driver.carInfo.brand} {trip.driver.carInfo.model} · {trip.driver.carInfo.color}
                  </Text>
                  <Text className='plate-number'>{trip.driver.carInfo.plateNumber}</Text>
                </View>
              )}

              {/* 行程要求 */}
              {trip.requirements && trip.requirements.length > 0 && (
                <View className='requirements'>
                  {trip.requirements.map((req, index) => (
                    <Text key={index} className='requirement-tag'>{req}</Text>
                  ))}
                </View>
              )}
            </View>
          ))
        ) : (
          <View className='empty-container'>
            <Text className='empty-icon'>🚗</Text>
            <Text className='empty-text'>暂无符合条件的行程</Text>
            <Text className='empty-desc'>试试调整搜索条件或稍后再试</Text>
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 */}
      <View className='bottom-actions'>
        <View className='publish-btn' onClick={() => Taro.navigateTo({ url: '/pages/publish/index' })}>
          <Text className='publish-text'>发布行程</Text>
        </View>
      </View>
    </View>
  );
};

export default SearchResultPage;
