.publish-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
  padding-bottom: 100px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    display: block;
  }

  // 行程类型选择
  .trip-type-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .trip-type-tabs {
      display: flex;
      gap: 16px;

      .tab {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        border-radius: 12px;
        border: 2px solid #e8e8e8;
        transition: all 0.3s;

        &.active {
          border-color: #007AFF;
          background: #f0f8ff;

          .tab-text {
            color: #007AFF;
            font-weight: 500;
          }
        }

        .tab-icon {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;

          &.driver-icon {
            background: #e6f7ff;
            
            &::after {
              content: '🚗';
            }
          }

          &.passenger-icon {
            background: #f6ffed;
            
            &::after {
              content: '👤';
            }
          }
        }

        .tab-text {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }

  // 路线信息
  .route-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .route-inputs {
      .route-item {
        display: flex;
        align-items: center;
        padding: 16px 0;

        .route-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 16px;
          flex-shrink: 0;

          &.start-dot {
            background: #52c41a;
          }

          &.end-dot {
            background: #ff4d4f;
          }
        }

        .route-content {
          flex: 1;

          .route-label {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
            display: block;
          }

          .route-text {
            font-size: 16px;
            color: #333;
            display: block;
          }
        }
      }

      .route-divider {
        width: 2px;
        height: 20px;
        background: #e8e8e8;
        margin-left: 22px;
      }
    }
  }

  // 出发时间
  .time-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .time-inputs {
      display: flex;
      gap: 16px;

      .time-item {
        flex: 1;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        text-align: center;

        .time-label {
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;
          display: block;
        }

        .time-value {
          font-size: 16px;
          color: #333;
          display: block;
        }
      }
    }
  }

  // 详细信息
  .details-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .details-inputs {
      .detail-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .detail-label {
          font-size: 16px;
          color: #333;
        }

        .detail-value {
          font-size: 16px;
          color: #007AFF;
          font-weight: 500;
        }

        .price-input {
          flex: 1;
          text-align: right;
          font-size: 16px;
          margin-right: 8px;
        }

        .price-unit {
          font-size: 14px;
          color: #999;
        }
      }
    }
  }

  // 车辆信息
  .car-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .car-inputs {
      .car-row {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .car-input {
          flex: 1;
          padding: 12px 16px;
          background: #f8f9fa;
          border-radius: 8px;
          font-size: 16px;
        }
      }
    }
  }

  // 出行要求
  .requirements-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .requirements-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;

      .requirement-tag {
        padding: 8px 16px;
        border-radius: 20px;
        border: 1px solid #e8e8e8;
        font-size: 14px;
        color: #666;
        background: #f8f9fa;
        transition: all 0.3s;

        &.active {
          background: #007AFF;
          color: #fff;
          border-color: #007AFF;
        }
      }
    }
  }

  // 备注说明
  .description-section {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 16px;

    .description-input {
      width: 100%;
      min-height: 80px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 8px;
      font-size: 16px;
      line-height: 1.5;
    }
  }

  // 发布按钮
  .publish-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #007AFF;
    color: #fff;
    padding: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    z-index: 100;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .publish-page {
    padding: 12px;
    padding-bottom: 80px;

    .section-title {
      font-size: 15px;
    }

    .trip-type-section,
    .route-section,
    .time-section,
    .details-section,
    .car-section,
    .requirements-section,
    .description-section {
      padding: 16px;
      margin-bottom: 12px;
    }

    .trip-type-section {
      .trip-type-tabs {
        .tab {
          padding: 16px;

          .tab-icon {
            width: 40px;
            height: 40px;
            font-size: 20px;
          }

          .tab-text {
            font-size: 15px;
          }
        }
      }
    }

    .time-section {
      .time-inputs {
        gap: 12px;

        .time-item {
          padding: 12px;
        }
      }
    }

    .publish-btn {
      padding: 16px;
      font-size: 16px;
    }
  }
}
