.chat-page {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  .header {
    background: #fff;
    padding: 44px 16px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 10;

    .back-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .back-icon {
        font-size: 20px;
        color: #333;
      }
    }

    .chat-info {
      flex: 1;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      .chat-name {
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }

      .verified-badge {
        background: #52c41a;
        color: #fff;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 8px;
      }
    }

    .more-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      .more-icon {
        font-size: 20px;
        color: #666;
      }
    }
  }

  .messages-container {
    flex: 1;
    padding: 16px;

    .message-wrapper {
      margin-bottom: 16px;

      .message-item {
        display: flex;
        align-items: flex-end;
        margin-bottom: 4px;

        &.sent {
          justify-content: flex-end;

          .message-content {
            background: #007AFF;
            margin-right: 12px;
            order: 1;

            .message-text {
              color: #fff;
            }
          }

          .message-avatar {
            order: 2;
          }
        }

        &.received {
          justify-content: flex-start;

          .message-content {
            background: #fff;
            margin-left: 12px;
            order: 2;

            .message-text {
              color: #333;
            }
          }

          .message-avatar {
            order: 1;
          }
        }

        .message-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background: #e8e8e8;
          flex-shrink: 0;
        }

        .message-content {
          max-width: 60%;
          padding: 12px 16px;
          border-radius: 18px;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);

          .message-text {
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
          }
        }
      }

      .message-time {
        font-size: 12px;
        color: #999;
        text-align: center;
        margin-bottom: 8px;

        &.sent {
          text-align: right;
          margin-right: 48px;
        }

        &.received {
          text-align: left;
          margin-left: 48px;
        }

        .time-text {
          background: rgba(255, 255, 255, 0.8);
          padding: 2px 8px;
          border-radius: 10px;
        }
      }
    }
  }

  .input-container {
    background: #fff;
    padding: 12px 16px;
    border-top: 1px solid #e8e8e8;

    .input-wrapper {
      display: flex;
      align-items: center;
      background: #f8f9fa;
      border-radius: 24px;
      padding: 8px 16px;

      .message-input {
        flex: 1;
        font-size: 16px;
        line-height: 1.4;
        min-height: 24px;
        max-height: 80px;
        background: transparent;
        border: none;
        outline: none;
      }

      .send-btn {
        background: #007AFF;
        color: #fff;
        padding: 8px 16px;
        border-radius: 16px;
        margin-left: 12px;

        .send-text {
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .chat-page {
    .header {
      padding: 40px 12px 12px;

      .chat-info {
        .chat-name {
          font-size: 16px;
        }
      }
    }

    .messages-container {
      padding: 12px;

      .message-wrapper {
        .message-item {
          .message-content {
            max-width: 70%;
            padding: 10px 14px;

            .message-text {
              font-size: 15px;
            }
          }

          .message-avatar {
            width: 32px;
            height: 32px;
          }
        }

        .message-time {
          &.sent {
            margin-right: 44px;
          }

          &.received {
            margin-left: 44px;
          }
        }
      }
    }

    .input-container {
      padding: 10px 12px;

      .input-wrapper {
        padding: 6px 12px;

        .message-input {
          font-size: 15px;
        }

        .send-btn {
          padding: 6px 12px;

          .send-text {
            font-size: 13px;
          }
        }
      }
    }
  }
}

/* 消息动画 */
.message-wrapper {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框聚焦效果 */
.input-wrapper:focus-within {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

/* 发送按钮禁用状态 */
.send-btn.disabled {
  background: #ccc;
  pointer-events: none;
}
