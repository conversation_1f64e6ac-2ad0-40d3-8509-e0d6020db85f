import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, Input, Image } from '@tarojs/components';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { chatApi } from '../../services/tripApi';
import { formatTime, showToast } from '../../utils/common';
import { Message, User } from '../../types/trip';
import './index.less';

interface ChatState {
  messages: Message[];
  inputText: string;
  loading: boolean;
  chatUser: User | null;
}

const ChatPage: React.FC = () => {
  const router = useRouter();
  const [state, setState] = useState<ChatState>({
    messages: [],
    inputText: '',
    loading: false,
    chatUser: null,
  });

  useDidShow(() => {
    const { userId, chatId } = router.params;
    if (userId || chatId) {
      loadChatData(userId, chatId);
    }
  });

  // 加载聊天数据
  const loadChatData = async (userId?: string, chatId?: string) => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 模拟聊天数据
      const mockUser: User = {
        id: userId || 'user1',
        name: '张师傅',
        phone: '138****8888',
        avatar: '',
        rating: 4.8,
        reviewCount: 156,
        isVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const mockMessages: Message[] = [
        {
          id: '1',
          chatId: chatId || 'chat1',
          senderId: userId || 'user1',
          content: '您好，我是司机张师傅',
          type: 'text',
          timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          isRead: true,
        },
        {
          id: '2',
          chatId: chatId || 'chat1',
          senderId: 'current_user',
          content: '您好，请问几点出发？',
          type: 'text',
          timestamp: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
          isRead: true,
        },
        {
          id: '3',
          chatId: chatId || 'chat1',
          senderId: userId || 'user1',
          content: '明天早上8点准时出发，请提前5分钟到达上车点',
          type: 'text',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          isRead: true,
        },
      ];

      setState(prev => ({
        ...prev,
        chatUser: mockUser,
        messages: mockMessages,
        loading: false,
      }));
    } catch (error: any) {
      console.error('加载聊天数据失败:', error);
      showToast(error.message || '加载失败');
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  // 发送消息
  const sendMessage = async () => {
    if (!state.inputText.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      chatId: 'chat1',
      senderId: 'current_user',
      content: state.inputText.trim(),
      type: 'text',
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage],
      inputText: '',
    }));

    try {
      // 这里应该调用实际的发送消息API
      // await chatApi.sendMessage(chatId, state.inputText.trim());
    } catch (error: any) {
      console.error('发送消息失败:', error);
      showToast('发送失败');
    }
  };

  // 返回上一页
  const goBack = () => {
    Taro.navigateBack();
  };

  return (
    <View className='chat-page'>
      {/* 顶部导航 */}
      <View className='header'>
        <View className='back-btn' onClick={goBack}>
          <Text className='back-icon'>←</Text>
        </View>
        <View className='chat-info'>
          {state.chatUser && (
            <>
              <Text className='chat-name'>{state.chatUser.name}</Text>
              {state.chatUser.isVerified && (
                <Text className='verified-badge'>已认证</Text>
              )}
            </>
          )}
        </View>
        <View className='more-btn'>
          <Text className='more-icon'>⋯</Text>
        </View>
      </View>

      {/* 消息列表 */}
      <ScrollView className='messages-container' scrollY scrollIntoView={`msg-${state.messages.length - 1}`}>
        {state.messages.map((message, index) => (
          <View key={message.id} id={`msg-${index}`} className='message-wrapper'>
            {message.senderId === 'current_user' ? (
              // 自己发送的消息
              <View className='message-item sent'>
                <View className='message-content'>
                  <Text className='message-text'>{message.content}</Text>
                </View>
                <Image 
                  className='message-avatar' 
                  src='/assets/icons/default-avatar.png' 
                />
              </View>
            ) : (
              // 对方发送的消息
              <View className='message-item received'>
                <Image 
                  className='message-avatar' 
                  src={state.chatUser?.avatar || '/assets/icons/default-avatar.png'} 
                />
                <View className='message-content'>
                  <Text className='message-text'>{message.content}</Text>
                </View>
              </View>
            )}
            <View className={`message-time ${message.senderId === 'current_user' ? 'sent' : 'received'}`}>
              <Text className='time-text'>{formatTime(message.timestamp, 'HH:mm')}</Text>
            </View>
          </View>
        ))}
      </ScrollView>

      {/* 输入框 */}
      <View className='input-container'>
        <View className='input-wrapper'>
          <Input
            className='message-input'
            placeholder='输入消息...'
            value={state.inputText}
            onInput={(e) => setState(prev => ({ ...prev, inputText: e.detail.value }))}
            confirmType='send'
            onConfirm={sendMessage}
          />
          <View className='send-btn' onClick={sendMessage}>
            <Text className='send-text'>发送</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ChatPage;
