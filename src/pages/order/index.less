.order-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 16px;

  .order-list {
    .order-item {
      background: white;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .order-id {
          font-size: 14px;
          color: #666;
        }

        .order-status {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;
          background: #007AFF;
          color: white;
        }
      }

      .order-content {
        .route-info {
          margin-bottom: 12px;

          .route-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .route-dot {
              width: 8px;
              height: 8px;
              border-radius: 4px;
              margin-right: 12px;
              background: #4CAF50;
            }

            .route-text {
              font-size: 14px;
              color: #333;
            }
          }
        }

        .order-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .price {
            font-size: 18px;
            font-weight: 600;
            color: #FF6B35;
          }

          .time {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #ccc;
    }

    .empty-text {
      font-size: 16px;
      color: #666;
    }
  }
}
