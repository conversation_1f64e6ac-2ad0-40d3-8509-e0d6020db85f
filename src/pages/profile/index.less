.profile-page {
  background: #f5f5f5;
  min-height: 100vh;

  .profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 44px 16px 24px;
    color: white;

    .user-info {
      display: flex;
      align-items: center;

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 30px;
        background: rgba(255, 255, 255, 0.2);
        margin-right: 16px;
      }

      .user-details {
        .name {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .phone {
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }
  }

  .menu-section {
    background: white;
    margin: 16px;
    border-radius: 12px;
    overflow: hidden;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .menu-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        font-size: 18px;
      }

      .menu-text {
        flex: 1;
        font-size: 16px;
        color: #333;
      }

      .menu-arrow {
        font-size: 14px;
        color: #ccc;
      }
    }
  }

  .logout-btn {
    margin: 16px;
    padding: 16px;
    background: #FF4757;
    color: white;
    border-radius: 12px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
  }
}
