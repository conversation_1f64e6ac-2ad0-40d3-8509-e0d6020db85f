import React, { useState } from 'react';
import { View, Text, Input, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { authApi } from '../../services/tripApi';
import { validatePhone, showToast, showLoading, hideLoading } from '../../utils/common';
import './index.less';

interface LoginState {
  loginType: 'sms' | 'password';
  phone: string;
  password: string;
  smsCode: string;
  countdown: number;
  isCountingDown: boolean;
  loading: boolean;
}

const LoginPage: React.FC = () => {
  const [state, setState] = useState<LoginState>({
    loginType: 'sms',
    phone: '',
    password: '',
    smsCode: '',
    countdown: 0,
    isCountingDown: false,
    loading: false,
  });

  // 切换登录方式
  const switchLoginType = (type: 'sms' | 'password') => {
    setState(prev => ({ ...prev, loginType: type }));
  };

  // 发送验证码
  const sendSmsCode = async () => {
    if (!validatePhone(state.phone)) {
      showToast('请输入正确的手机号');
      return;
    }

    if (state.isCountingDown) {
      return;
    }

    try {
      showLoading('发送中...');
      await authApi.sendSmsCode(state.phone, 'login');
      hideLoading();
      showToast('验证码已发送', 'success');
      
      // 开始倒计时
      setState(prev => ({ ...prev, countdown: 60, isCountingDown: true }));
      const timer = setInterval(() => {
        setState(prev => {
          if (prev.countdown <= 1) {
            clearInterval(timer);
            return { ...prev, countdown: 0, isCountingDown: false };
          }
          return { ...prev, countdown: prev.countdown - 1 };
        });
      }, 1000);
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '发送失败');
    }
  };

  // 验证码登录
  const loginWithSms = async () => {
    if (!validatePhone(state.phone)) {
      showToast('请输入正确的手机号');
      return;
    }

    if (!state.smsCode) {
      showToast('请输入验证码');
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true }));
      showLoading('登录中...');
      
      const response = await authApi.loginWithSms(state.phone, state.smsCode);
      
      if (response.success && response.data) {
        // 保存用户信息和token
        Taro.setStorageSync('token', response.data.token);
        Taro.setStorageSync('user', response.data.user);
        
        hideLoading();
        showToast('登录成功', 'success');
        
        // 跳转到首页
        setTimeout(() => {
          Taro.switchTab({ url: '/pages/home/<USER>' });
        }, 1000);
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '登录失败');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  // 密码登录
  const loginWithPassword = async () => {
    if (!validatePhone(state.phone)) {
      showToast('请输入正确的手机号');
      return;
    }

    if (!state.password) {
      showToast('请输入密码');
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true }));
      showLoading('登录中...');
      
      const response = await authApi.loginWithPassword(state.phone, state.password);
      
      if (response.success && response.data) {
        // 保存用户信息和token
        Taro.setStorageSync('token', response.data.token);
        Taro.setStorageSync('user', response.data.user);
        
        hideLoading();
        showToast('登录成功', 'success');
        
        // 跳转到首页
        setTimeout(() => {
          Taro.switchTab({ url: '/pages/home/<USER>' });
        }, 1000);
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '登录失败');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  // 微信登录
  const loginWithWechat = async () => {
    try {
      const loginRes = await Taro.login();
      if (loginRes.code) {
        showLoading('登录中...');
        const response = await authApi.loginWithWechat(loginRes.code);
        
        if (response.success && response.data) {
          // 保存用户信息和token
          Taro.setStorageSync('token', response.data.token);
          Taro.setStorageSync('user', response.data.user);
          
          hideLoading();
          showToast('登录成功', 'success');
          
          // 跳转到首页
          setTimeout(() => {
            Taro.switchTab({ url: '/pages/home/<USER>' });
          }, 1000);
        } else {
          throw new Error(response.message || '登录失败');
        }
      }
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '微信登录失败');
    }
  };

  // 处理登录
  const handleLogin = () => {
    if (state.loginType === 'sms') {
      loginWithSms();
    } else {
      loginWithPassword();
    }
  };

  return (
    <View className='login-page'>
      <View className='header'>
        <Text className='title'>白菜出行</Text>
        <Text className='subtitle'>安全便捷的出行服务</Text>
      </View>

      <View className='form'>
        {/* 登录方式切换 */}
        <View className='login-type-tabs'>
          <View 
            className={`tab ${state.loginType === 'sms' ? 'active' : ''}`}
            onClick={() => switchLoginType('sms')}
          >
            验证码登录
          </View>
          <View 
            className={`tab ${state.loginType === 'password' ? 'active' : ''}`}
            onClick={() => switchLoginType('password')}
          >
            密码登录
          </View>
        </View>

        {/* 手机号输入 */}
        <View className='input-group'>
          <Input
            className='input'
            type='number'
            placeholder='请输入手机号'
            value={state.phone}
            onInput={(e) => setState(prev => ({ ...prev, phone: e.detail.value }))}
            maxlength={11}
          />
        </View>

        {/* 验证码登录 */}
        {state.loginType === 'sms' && (
          <View className='input-group sms-group'>
            <Input
              className='input sms-input'
              type='number'
              placeholder='请输入验证码'
              value={state.smsCode}
              onInput={(e) => setState(prev => ({ ...prev, smsCode: e.detail.value }))}
              maxlength={6}
            />
            <View 
              className={`sms-btn ${state.isCountingDown ? 'disabled' : ''}`}
              onClick={sendSmsCode}
            >
              {state.isCountingDown ? `${state.countdown}s` : '获取验证码'}
            </View>
          </View>
        )}

        {/* 密码登录 */}
        {state.loginType === 'password' && (
          <View className='input-group'>
            <Input
              className='input'
              type='password'
              placeholder='请输入密码'
              value={state.password}
              onInput={(e) => setState(prev => ({ ...prev, password: e.detail.value }))}
            />
          </View>
        )}

        {/* 登录按钮 */}
        <Button 
          className='login-btn'
          onClick={handleLogin}
          disabled={state.loading}
        >
          {state.loading ? '登录中...' : '登录'}
        </Button>

        {/* 微信登录 */}
        <Button 
          className='wechat-btn'
          onClick={loginWithWechat}
        >
          微信一键登录
        </Button>

        {/* 底部链接 */}
        <View className='footer-links'>
          <Text 
            className='link'
            onClick={() => Taro.navigateTo({ url: '/pages/register/index' })}
          >
            注册账号
          </Text>
          {state.loginType === 'password' && (
            <Text 
              className='link'
              onClick={() => Taro.navigateTo({ url: '/pages/forgot-password/index' })}
            >
              忘记密码
            </Text>
          )}
        </View>

        {/* 协议 */}
        <View className='agreement'>
          <Text className='agreement-text'>
            登录即表示同意
            <Text className='link'>《用户协议》</Text>
            和
            <Text className='link'>《隐私政策》</Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

export default LoginPage;
