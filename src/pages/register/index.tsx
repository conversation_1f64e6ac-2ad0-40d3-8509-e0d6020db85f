import React, { useState } from 'react';
import { View, Text, Input, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { authApi } from '../../services/tripApi';
import { validatePhone, showToast, showLoading, hideLoading } from '../../utils/common';
import './index.less';

interface RegisterState {
  phone: string;
  smsCode: string;
  password: string;
  confirmPassword: string;
  name: string;
  countdown: number;
  isCountingDown: boolean;
  loading: boolean;
  agreed: boolean;
}

const RegisterPage: React.FC = () => {
  const [state, setState] = useState<RegisterState>({
    phone: '',
    smsCode: '',
    password: '',
    confirmPassword: '',
    name: '',
    countdown: 0,
    isCountingDown: false,
    loading: false,
    agreed: false,
  });

  // 发送验证码
  const sendSmsCode = async () => {
    if (!validatePhone(state.phone)) {
      showToast('请输入正确的手机号');
      return;
    }

    if (state.isCountingDown) {
      return;
    }

    try {
      showLoading('发送中...');
      await authApi.sendSmsCode(state.phone, 'register');
      hideLoading();
      showToast('验证码已发送', 'success');
      
      // 开始倒计时
      setState(prev => ({ ...prev, countdown: 60, isCountingDown: true }));
      const timer = setInterval(() => {
        setState(prev => {
          if (prev.countdown <= 1) {
            clearInterval(timer);
            return { ...prev, countdown: 0, isCountingDown: false };
          }
          return { ...prev, countdown: prev.countdown - 1 };
        });
      }, 1000);
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '发送失败');
    }
  };

  // 注册
  const handleRegister = async () => {
    // 表单验证
    if (!validatePhone(state.phone)) {
      showToast('请输入正确的手机号');
      return;
    }

    if (!state.smsCode) {
      showToast('请输入验证码');
      return;
    }

    if (!state.name.trim()) {
      showToast('请输入姓名');
      return;
    }

    if (state.password.length < 6) {
      showToast('密码至少6位');
      return;
    }

    if (state.password !== state.confirmPassword) {
      showToast('两次输入的密码不一致');
      return;
    }

    if (!state.agreed) {
      showToast('请同意用户协议和隐私政策');
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true }));
      showLoading('注册中...');
      
      const response = await authApi.register(
        state.phone,
        state.smsCode,
        state.password,
        state.name.trim()
      );
      
      if (response.success && response.data) {
        // 保存用户信息和token
        Taro.setStorageSync('token', response.data.token);
        Taro.setStorageSync('user', response.data.user);
        
        hideLoading();
        showToast('注册成功', 'success');
        
        // 跳转到首页
        setTimeout(() => {
          Taro.switchTab({ url: '/pages/home/<USER>' });
        }, 1000);
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '注册失败');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <View className='register-page'>
      <View className='header'>
        <Text className='title'>注册账号</Text>
        <Text className='subtitle'>加入白菜出行大家庭</Text>
      </View>

      <View className='form'>
        {/* 手机号输入 */}
        <View className='input-group'>
          <Input
            className='input'
            type='number'
            placeholder='请输入手机号'
            value={state.phone}
            onInput={(e) => setState(prev => ({ ...prev, phone: e.detail.value }))}
            maxlength={11}
          />
        </View>

        {/* 验证码输入 */}
        <View className='input-group sms-group'>
          <Input
            className='input sms-input'
            type='number'
            placeholder='请输入验证码'
            value={state.smsCode}
            onInput={(e) => setState(prev => ({ ...prev, smsCode: e.detail.value }))}
            maxlength={6}
          />
          <View 
            className={`sms-btn ${state.isCountingDown ? 'disabled' : ''}`}
            onClick={sendSmsCode}
          >
            {state.isCountingDown ? `${state.countdown}s` : '获取验证码'}
          </View>
        </View>

        {/* 姓名输入 */}
        <View className='input-group'>
          <Input
            className='input'
            placeholder='请输入真实姓名'
            value={state.name}
            onInput={(e) => setState(prev => ({ ...prev, name: e.detail.value }))}
            maxlength={20}
          />
        </View>

        {/* 密码输入 */}
        <View className='input-group'>
          <Input
            className='input'
            type='password'
            placeholder='请设置密码（至少6位）'
            value={state.password}
            onInput={(e) => setState(prev => ({ ...prev, password: e.detail.value }))}
          />
        </View>

        {/* 确认密码 */}
        <View className='input-group'>
          <Input
            className='input'
            type='password'
            placeholder='请再次输入密码'
            value={state.confirmPassword}
            onInput={(e) => setState(prev => ({ ...prev, confirmPassword: e.detail.value }))}
          />
        </View>

        {/* 协议同意 */}
        <View className='agreement'>
          <View 
            className={`checkbox ${state.agreed ? 'checked' : ''}`}
            onClick={() => setState(prev => ({ ...prev, agreed: !prev.agreed }))}
          >
            {state.agreed && <Text className='checkmark'>✓</Text>}
          </View>
          <Text className='agreement-text'>
            我已阅读并同意
            <Text className='link'>《用户协议》</Text>
            和
            <Text className='link'>《隐私政策》</Text>
          </Text>
        </View>

        {/* 注册按钮 */}
        <Button 
          className='register-btn'
          onClick={handleRegister}
          disabled={state.loading}
        >
          {state.loading ? '注册中...' : '注册'}
        </Button>

        {/* 登录链接 */}
        <View className='footer-links'>
          <Text className='login-text'>已有账号？</Text>
          <Text 
            className='link'
            onClick={() => Taro.navigateBack()}
          >
            立即登录
          </Text>
        </View>
      </View>
    </View>
  );
};

export default RegisterPage;
