.trip-detail-page {
  width: 100vw;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  &.loading {
    justify-content: center;
    align-items: center;
  }

  .header {
    background: #fff;
    padding: 44px 16px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 10;

    .back-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .back-icon {
        font-size: 20px;
        color: #333;
      }
    }

    .header-title {
      flex: 1;
      text-align: center;
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .share-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      .share-icon {
        font-size: 20px;
        color: #666;
      }
    }
  }

  .content {
    flex: 1;
    padding: 16px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      display: block;
    }

    // 司机信息
    .driver-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .driver-info {
        display: flex;
        align-items: center;

        .driver-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin-right: 16px;
          background: #e8e8e8;
        }

        .driver-details {
          flex: 1;

          .driver-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            display: block;
          }

          .driver-rating {
            display: flex;
            align-items: center;
            gap: 8px;

            .rating-text {
              font-size: 14px;
              color: #ff9500;
            }

            .review-count {
              font-size: 12px;
              color: #999;
            }

            .verified-badge {
              background: #52c41a;
              color: #fff;
              font-size: 10px;
              padding: 2px 6px;
              border-radius: 8px;
            }
          }
        }

        .contact-btn {
          background: #007AFF;
          color: #fff;
          padding: 8px 16px;
          border-radius: 20px;

          .contact-text {
            font-size: 14px;
            color: #fff;
          }
        }
      }
    }

    // 行程信息
    .trip-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .trip-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .trip-price {
          font-size: 28px;
          font-weight: 700;
          color: #ff6b35;
        }

        .price-unit {
          font-size: 14px;
          color: #999;
          margin-left: 4px;
          margin-right: auto;
        }

        .trip-status {
          background: #e6f7ff;
          padding: 4px 12px;
          border-radius: 12px;

          .status-text {
            font-size: 12px;
            color: #1890ff;
          }
        }
      }

      .trip-time {
        margin-bottom: 24px;

        .departure-time {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }

        .time-desc {
          font-size: 12px;
          color: #999;
          display: block;
        }
      }

      .trip-route {
        margin-bottom: 24px;

        .route-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .route-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 16px;
            margin-top: 6px;
            flex-shrink: 0;

            &.start-dot {
              background: #52c41a;
            }

            &.end-dot {
              background: #ff4d4f;
            }
          }

          .route-info {
            flex: 1;

            .location-name {
              font-size: 16px;
              font-weight: 500;
              color: #333;
              display: block;
              margin-bottom: 4px;
            }

            .location-address {
              font-size: 14px;
              color: #666;
              display: block;
              line-height: 1.4;
            }
          }
        }

        .route-line {
          width: 2px;
          height: 20px;
          background: linear-gradient(to bottom, #52c41a, #ff4d4f);
          margin-left: 22px;
          margin-bottom: 16px;
        }
      }

      .trip-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-size: 14px;
            color: #666;
          }

          .detail-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }
        }
      }
    }

    // 车辆信息
    .car-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .car-info {
        .car-model {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
          display: block;
        }

        .car-details {
          font-size: 14px;
          color: #666;
          display: block;
        }
      }
    }

    // 出行要求
    .requirements-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .requirements-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .requirement-tag {
          background: #e6f7ff;
          color: #1890ff;
          font-size: 12px;
          padding: 6px 12px;
          border-radius: 16px;
          border: 1px solid #91d5ff;
        }
      }
    }

    // 备注说明
    .description-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .description-text {
        font-size: 14px;
        color: #666;
        line-height: 1.6;
        display: block;
      }
    }

    // 已预订乘客
    .passengers-section {
      background: #fff;
      border-radius: 16px;
      padding: 20px;
      margin-bottom: 16px;

      .passenger-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .passenger-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 12px;
          background: #e8e8e8;
        }

        .passenger-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .verified-badge {
          background: #52c41a;
          color: #fff;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 8px;
        }
      }
    }
  }

  // 底部操作栏
  .bottom-actions {
    background: #fff;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);

    .seats-selector {
      display: flex;
      align-items: center;
      gap: 12px;

      .seats-label {
        font-size: 14px;
        color: #666;
      }

      .seats-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .seats-btn {
          width: 32px;
          height: 32px;
          background: #f0f0f0;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #666;
        }

        .seats-count {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          min-width: 24px;
          text-align: center;
        }
      }
    }

    .book-btn {
      flex: 1;
      background: #007AFF;
      padding: 16px;
      border-radius: 12px;
      text-align: center;

      .book-text {
        font-size: 16px;
        font-weight: 600;
        color: #fff;
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .trip-detail-page {
    .header {
      padding: 40px 12px 12px;

      .header-title {
        font-size: 16px;
      }
    }

    .content {
      padding: 12px;

      .driver-section,
      .trip-section,
      .car-section,
      .requirements-section,
      .description-section,
      .passengers-section {
        padding: 16px;
        margin-bottom: 12px;
      }

      .driver-section {
        .driver-info {
          .driver-avatar {
            width: 50px;
            height: 50px;
          }

          .driver-details {
            .driver-name {
              font-size: 16px;
            }
          }
        }
      }

      .trip-section {
        .trip-header {
          .trip-price {
            font-size: 24px;
          }
        }

        .trip-time {
          .departure-time {
            font-size: 18px;
          }
        }
      }
    }

    .bottom-actions {
      padding: 12px;
    }
  }
}
