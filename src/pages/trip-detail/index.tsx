import React, { useState, useEffect } from 'react';
import { View, Text, Image, ScrollView, Button } from '@tarojs/components';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { tripApi, orderApi } from '../../services/tripApi';
import { formatTime, formatPrice, showToast, showLoading, hideLoading, showConfirm } from '../../utils/common';
import { Trip, Order } from '../../types/trip';
import './index.less';

interface TripDetailState {
  trip: Trip | null;
  loading: boolean;
  booking: boolean;
  seats: number;
}

const TripDetailPage: React.FC = () => {
  const router = useRouter();
  const [state, setState] = useState<TripDetailState>({
    trip: null,
    loading: false,
    booking: false,
    seats: 1,
  });

  useDidShow(() => {
    const { id } = router.params;
    if (id) {
      loadTripDetail(id);
    }
  });

  // 加载行程详情
  const loadTripDetail = async (tripId: string) => {
    try {
      setState(prev => ({ ...prev, loading: true }));
      showLoading('加载中...');

      const response = await tripApi.getTripDetail(tripId);
      
      if (response.success) {
        setState(prev => ({ 
          ...prev, 
          trip: response.data,
          loading: false 
        }));
      } else {
        throw new Error(response.message || '加载失败');
      }
    } catch (error: any) {
      console.error('加载行程详情失败:', error);
      showToast(error.message || '加载失败');
      setState(prev => ({ ...prev, loading: false }));
    } finally {
      hideLoading();
    }
  };

  // 预订行程
  const bookTrip = async () => {
    if (!state.trip) return;

    if (state.seats > state.trip.availableSeats) {
      showToast('座位数不足');
      return;
    }

    const confirmed = await showConfirm(
      `确认预订 ${state.seats} 个座位，总价 ${formatPrice(state.trip.price * state.seats)}？`
    );

    if (!confirmed) return;

    try {
      setState(prev => ({ ...prev, booking: true }));
      showLoading('预订中...');

      const response = await orderApi.createOrder(state.trip.id, state.seats);
      
      if (response.success) {
        hideLoading();
        showToast('预订成功', 'success');
        
        // 跳转到订单页面
        setTimeout(() => {
          Taro.redirectTo({
            url: `/pages/order/index`,
          });
        }, 1000);
      } else {
        throw new Error(response.message || '预订失败');
      }
    } catch (error: any) {
      hideLoading();
      showToast(error.message || '预订失败');
    } finally {
      setState(prev => ({ ...prev, booking: false }));
    }
  };

  // 联系司机
  const contactDriver = () => {
    if (!state.trip) return;
    
    Taro.navigateTo({
      url: `/pages/chat/index?userId=${state.trip.driver.id}`,
    });
  };

  // 返回上一页
  const goBack = () => {
    Taro.navigateBack();
  };

  if (state.loading || !state.trip) {
    return (
      <View className='trip-detail-page loading'>
        <Text>加载中...</Text>
      </View>
    );
  }

  const { trip } = state;

  return (
    <View className='trip-detail-page'>
      {/* 顶部导航 */}
      <View className='header'>
        <View className='back-btn' onClick={goBack}>
          <Text className='back-icon'>←</Text>
        </View>
        <Text className='header-title'>行程详情</Text>
        <View className='share-btn'>
          <Text className='share-icon'>⋯</Text>
        </View>
      </View>

      <ScrollView className='content' scrollY>
        {/* 司机信息 */}
        <View className='driver-section'>
          <View className='driver-info'>
            <Image 
              className='driver-avatar' 
              src={trip.driver.avatar || '/assets/icons/default-avatar.png'} 
            />
            <View className='driver-details'>
              <Text className='driver-name'>{trip.driver.name}</Text>
              <View className='driver-rating'>
                <Text className='rating-text'>⭐ {trip.driver.rating || 5.0}</Text>
                <Text className='review-count'>({trip.driver.reviewCount || 0}条评价)</Text>
                {trip.driver.isVerified && (
                  <Text className='verified-badge'>已认证</Text>
                )}
              </View>
            </View>
            <View className='contact-btn' onClick={contactDriver}>
              <Text className='contact-text'>联系</Text>
            </View>
          </View>
        </View>

        {/* 行程信息 */}
        <View className='trip-section'>
          <View className='trip-header'>
            <Text className='trip-price'>{formatPrice(trip.price)}</Text>
            <Text className='price-unit'>/人</Text>
            <View className='trip-status'>
              <Text className='status-text'>
                {trip.status === 'pending' ? '待出发' : 
                 trip.status === 'confirmed' ? '已确认' : 
                 trip.status === 'in_progress' ? '进行中' : 
                 trip.status === 'completed' ? '已完成' : '已取消'}
              </Text>
            </View>
          </View>

          <View className='trip-time'>
            <Text className='departure-time'>{formatTime(trip.departureTime, 'MM月DD日 HH:mm')}</Text>
            <Text className='time-desc'>出发时间</Text>
          </View>

          <View className='trip-route'>
            <View className='route-item'>
              <View className='route-dot start-dot' />
              <View className='route-info'>
                <Text className='location-name'>{trip.startLocation.name || '起点'}</Text>
                <Text className='location-address'>{trip.startLocation.address}</Text>
              </View>
            </View>
            
            <View className='route-line' />
            
            <View className='route-item'>
              <View className='route-dot end-dot' />
              <View className='route-info'>
                <Text className='location-name'>{trip.endLocation.name || '终点'}</Text>
                <Text className='location-address'>{trip.endLocation.address}</Text>
              </View>
            </View>
          </View>

          <View className='trip-details'>
            <View className='detail-item'>
              <Text className='detail-label'>可载人数</Text>
              <Text className='detail-value'>{trip.availableSeats}/{trip.seats}人</Text>
            </View>
            {trip.distance && (
              <View className='detail-item'>
                <Text className='detail-label'>行程距离</Text>
                <Text className='detail-value'>{trip.distance}km</Text>
              </View>
            )}
            {trip.duration && (
              <View className='detail-item'>
                <Text className='detail-label'>预计时长</Text>
                <Text className='detail-value'>{Math.round(trip.duration / 60)}分钟</Text>
              </View>
            )}
          </View>
        </View>

        {/* 车辆信息 */}
        {trip.driver.carInfo && (
          <View className='car-section'>
            <Text className='section-title'>车辆信息</Text>
            <View className='car-info'>
              <Text className='car-model'>
                {trip.driver.carInfo.brand} {trip.driver.carInfo.model}
              </Text>
              <Text className='car-details'>
                {trip.driver.carInfo.color} · {trip.driver.carInfo.plateNumber}
              </Text>
            </View>
          </View>
        )}

        {/* 出行要求 */}
        {trip.requirements && trip.requirements.length > 0 && (
          <View className='requirements-section'>
            <Text className='section-title'>出行要求</Text>
            <View className='requirements-tags'>
              {trip.requirements.map((req, index) => (
                <Text key={index} className='requirement-tag'>{req}</Text>
              ))}
            </View>
          </View>
        )}

        {/* 备注说明 */}
        {trip.description && (
          <View className='description-section'>
            <Text className='section-title'>备注说明</Text>
            <Text className='description-text'>{trip.description}</Text>
          </View>
        )}

        {/* 已预订乘客 */}
        {trip.passengers && trip.passengers.length > 0 && (
          <View className='passengers-section'>
            <Text className='section-title'>已预订乘客</Text>
            {trip.passengers.map((passenger) => (
              <View key={passenger.id} className='passenger-item'>
                <Image 
                  className='passenger-avatar' 
                  src={passenger.avatar || '/assets/icons/default-avatar.png'} 
                />
                <Text className='passenger-name'>{passenger.name}</Text>
                {passenger.isVerified && (
                  <Text className='verified-badge'>已认证</Text>
                )}
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 */}
      {trip.status === 'pending' && trip.availableSeats > 0 && (
        <View className='bottom-actions'>
          <View className='seats-selector'>
            <Text className='seats-label'>座位数:</Text>
            <View className='seats-controls'>
              <View 
                className='seats-btn'
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  seats: Math.max(1, prev.seats - 1) 
                }))}
              >
                <Text>-</Text>
              </View>
              <Text className='seats-count'>{state.seats}</Text>
              <View 
                className='seats-btn'
                onClick={() => setState(prev => ({ 
                  ...prev, 
                  seats: Math.min(trip.availableSeats, prev.seats + 1) 
                }))}
              >
                <Text>+</Text>
              </View>
            </View>
          </View>
          <View className='book-btn' onClick={bookTrip}>
            <Text className='book-text'>
              预订 {formatPrice(trip.price * state.seats)}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

export default TripDetailPage;
