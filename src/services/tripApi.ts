import Taro from '@tarojs/taro';
import {
  ApiResponse,
  Trip,
  Order,
  User,
  LoginParams,
  RegisterParams,
  PublishTripParams,
  SearchParams,
  AuthInfo,
} from '../types/trip';
import { mockTrips, mockOrders, mockUsers, getMockSearchResults, mockApiDelay } from '../data/mockData';

// API基础配置
const API_BASE_URL = 'https://api.trip.com'; // 替换为实际的API地址
const TIMEOUT = 10000;

// 请求拦截器
const request = async <T = any>(
  url: string,
  options: Taro.request.Option = {}
): Promise<ApiResponse<T>> => {
  try {
    // 获取token
    const token = Taro.getStorageSync('token');
    
    const response = await Taro.request({
      url: `${API_BASE_URL}${url}`,
      timeout: TIMEOUT,
      header: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.header,
      },
      ...options,
    });

    if (response.statusCode === 200) {
      return response.data as ApiResponse<T>;
    } else {
      throw new Error(`请求失败: ${response.statusCode}`);
    }
  } catch (error: any) {
    console.error('API请求错误:', error);
    return {
      success: false,
      message: error.message || '网络请求失败',
    };
  }
};

// 模拟API请求（开发阶段使用）
const mockRequest = async <T = any>(
  mockData: T,
  delay: number = 1000
): Promise<ApiResponse<T>> => {
  await mockApiDelay(delay);
  return {
    success: true,
    data: mockData,
  };
};

// 认证相关API
export const authApi = {
  // 登录
  login: async (params: LoginParams): Promise<ApiResponse<AuthInfo>> => {
    // 模拟登录逻辑
    const user = mockUsers.find(u => u.phone === params.phone);
    if (!user) {
      return {
        success: false,
        message: '用户不存在',
      };
    }

    const authInfo: AuthInfo = {
      token: 'mock_token_' + Date.now(),
      user,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    };

    // 保存token到本地
    Taro.setStorageSync('token', authInfo.token);
    Taro.setStorageSync('user', authInfo.user);

    return mockRequest(authInfo);
  },

  // 注册
  register: async (params: RegisterParams): Promise<ApiResponse<AuthInfo>> => {
    // 模拟注册逻辑
    const newUser: User = {
      id: 'user_' + Date.now(),
      phone: params.phone,
      name: params.name,
      avatar: '',
      isVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const authInfo: AuthInfo = {
      token: 'mock_token_' + Date.now(),
      user: newUser,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    };

    // 保存token到本地
    Taro.setStorageSync('token', authInfo.token);
    Taro.setStorageSync('user', authInfo.user);

    return mockRequest(authInfo);
  },

  // 发送短信验证码
  sendSmsCode: async (phone: string): Promise<ApiResponse<void>> => {
    return mockRequest(undefined, 500);
  },

  // 登出
  logout: async (): Promise<ApiResponse<void>> => {
    Taro.removeStorageSync('token');
    Taro.removeStorageSync('user');
    return mockRequest(undefined, 500);
  },

  // 获取用户信息
  getUserInfo: async (): Promise<ApiResponse<User>> => {
    const user = Taro.getStorageSync('user');
    return mockRequest(user || mockUsers[0]);
  },

  // 更新用户信息
  updateUserInfo: async (userInfo: Partial<User>): Promise<ApiResponse<User>> => {
    const currentUser = Taro.getStorageSync('user') || mockUsers[0];
    const updatedUser = { ...currentUser, ...userInfo };
    Taro.setStorageSync('user', updatedUser);
    return mockRequest(updatedUser);
  },
};

// 行程相关API
export const tripApi = {
  // 搜索行程
  searchTrips: async (params: SearchParams): Promise<ApiResponse<Trip[]>> => {
    const results = getMockSearchResults(params);
    return mockRequest(results);
  },

  // 获取行程详情
  getTripDetail: async (tripId: string): Promise<ApiResponse<Trip>> => {
    const trip = mockTrips.find(t => t.id === tripId);
    if (!trip) {
      return {
        success: false,
        message: '行程不存在',
      };
    }
    return mockRequest(trip);
  },

  // 发布行程
  publishTrip: async (params: PublishTripParams): Promise<ApiResponse<Trip>> => {
    const currentUser = Taro.getStorageSync('user') || mockUsers[0];
    
    const newTrip: Trip = {
      id: 'trip_' + Date.now(),
      type: params.type,
      status: 'pending',
      driver: currentUser,
      passengers: [],
      startLocation: params.startLocation!,
      endLocation: params.endLocation!,
      departureTime: params.departureTime,
      price: params.price,
      seats: params.seats,
      availableSeats: params.seats,
      description: params.description,
      requirements: params.requirements,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return mockRequest(newTrip);
  },

  // 取消行程
  cancelTrip: async (tripId: string): Promise<ApiResponse<void>> => {
    return mockRequest(undefined);
  },

  // 获取我的行程
  getMyTrips: async (status?: string): Promise<ApiResponse<Trip[]>> => {
    const currentUser = Taro.getStorageSync('user') || mockUsers[0];
    const myTrips = mockTrips.filter(trip => 
      trip.driver.id === currentUser.id || 
      trip.passengers.some(p => p.id === currentUser.id)
    );
    return mockRequest(myTrips);
  },
};

// 订单相关API
export const orderApi = {
  // 创建订单
  createOrder: async (tripId: string, seats: number): Promise<ApiResponse<Order>> => {
    const currentUser = Taro.getStorageSync('user') || mockUsers[0];
    const trip = mockTrips.find(t => t.id === tripId);
    
    if (!trip) {
      return {
        success: false,
        message: '行程不存在',
      };
    }

    const newOrder: Order = {
      id: 'order_' + Date.now(),
      tripId,
      trip,
      passenger: currentUser,
      driver: trip.driver,
      status: 'pending',
      seats,
      totalPrice: trip.price * seats,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return mockRequest(newOrder);
  },

  // 获取订单列表
  getOrders: async (status?: string): Promise<ApiResponse<Order[]>> => {
    const currentUser = Taro.getStorageSync('user') || mockUsers[0];
    let orders = mockOrders.filter(order => 
      order.passenger.id === currentUser.id || 
      order.driver.id === currentUser.id
    );

    if (status && status !== 'all') {
      orders = orders.filter(order => order.status === status);
    }

    return mockRequest(orders);
  },

  // 获取订单详情
  getOrderDetail: async (orderId: string): Promise<ApiResponse<Order>> => {
    const order = mockOrders.find(o => o.id === orderId);
    if (!order) {
      return {
        success: false,
        message: '订单不存在',
      };
    }
    return mockRequest(order);
  },

  // 确认订单
  confirmOrder: async (orderId: string): Promise<ApiResponse<void>> => {
    return mockRequest(undefined);
  },

  // 取消订单
  cancelOrder: async (orderId: string): Promise<ApiResponse<void>> => {
    return mockRequest(undefined);
  },

  // 完成订单
  completeOrder: async (orderId: string): Promise<ApiResponse<void>> => {
    return mockRequest(undefined);
  },
};

// 聊天相关API
export const chatApi = {
  // 获取聊天列表
  getChatList: async (): Promise<ApiResponse<any[]>> => {
    return mockRequest([]);
  },

  // 获取聊天消息
  getChatMessages: async (chatId: string): Promise<ApiResponse<any[]>> => {
    return mockRequest([]);
  },

  // 发送消息
  sendMessage: async (chatId: string, content: string): Promise<ApiResponse<any>> => {
    return mockRequest({});
  },
};
