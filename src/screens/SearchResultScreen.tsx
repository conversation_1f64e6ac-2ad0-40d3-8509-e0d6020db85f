import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { Trip, SearchParams, Location } from '../types/trip';
import { apiService } from '../services/api';

interface SearchResultProps {
  route: {
    params: {
      searchParams: SearchParams;
    };
  };
  navigation: any;
}

const SearchResultScreen: React.FC<SearchResultProps> = ({ route, navigation }) => {
  const { searchParams } = route.params;
  const [trips, setTrips] = useState<Trip[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    searchTrips();
  }, []);

  // 搜索行程
  const searchTrips = async () => {
    try {
      setLoading(true);
      const response = await apiService.searchTrips(searchParams);
      
      if (response.success && response.data) {
        setTrips(response.data);
      } else {
        Alert.alert('提示', response.message || '搜索失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '搜索失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 下拉刷新
  const onRefresh = async () => {
    setRefreshing(true);
    await searchTrips();
    setRefreshing(false);
  };

  // 预订行程
  const bookTrip = async (trip: Trip) => {
    try {
      Alert.alert(
        '确认预订',
        `确定要预订这个行程吗？\n价格：¥${trip.price}/人`,
        [
          { text: '取消', style: 'cancel' },
          {
            text: '确定',
            onPress: async () => {
              const response = await apiService.createOrder(trip.id, 1);
              if (response.success) {
                Alert.alert('成功', '预订成功！', [
                  {
                    text: '查看订单',
                    onPress: () => navigation.navigate('Orders'),
                  },
                  { text: '确定' },
                ]);
              } else {
                Alert.alert('失败', response.message || '预订失败');
              }
            },
          },
        ]
      );
    } catch (error: any) {
      Alert.alert('错误', error.message || '预订失败，请重试');
    }
  };

  // 查看行程详情
  const viewTripDetail = (trip: Trip) => {
    navigation.navigate('TripDetail', { tripId: trip.id });
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 渲染行程卡片
  const renderTripCard = (trip: Trip) => (
    <TouchableOpacity
      key={trip.id}
      style={styles.tripCard}
      onPress={() => viewTripDetail(trip)}
    >
      {/* 路线信息 */}
      <View style={styles.routeInfo}>
        <View style={styles.routeHeader}>
          <Text style={styles.startLocation}>{trip.startLocation.name || trip.startLocation.address}</Text>
          <View style={styles.routeArrow}>
            <Text style={styles.arrowText}>→</Text>
          </View>
          <Text style={styles.endLocation}>{trip.endLocation.name || trip.endLocation.address}</Text>
        </View>
        
        <View style={styles.timeInfo}>
          <Text style={styles.departureTime}>{formatTime(trip.departureTime)}</Text>
          <Text style={styles.availableSeats}>剩余 {trip.availableSeats} 座</Text>
        </View>
      </View>

      {/* 司机信息 */}
      {trip.driver && (
        <View style={styles.driverInfo}>
          <View style={styles.driverAvatar}>
            <Text style={styles.driverAvatarText}>
              {trip.driver.name.charAt(0)}
            </Text>
          </View>
          
          <View style={styles.driverDetails}>
            <View style={styles.driverHeader}>
              <Text style={styles.driverName}>{trip.driver.name}</Text>
              {trip.driver.verified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedText}>✓</Text>
                </View>
              )}
              {trip.driver.rating && (
                <View style={styles.ratingContainer}>
                  <Text style={styles.ratingText}>{trip.driver.rating.toFixed(1)}</Text>
                  <Text style={styles.ratingStar}>⭐</Text>
                </View>
              )}
            </View>
            
            {trip.driver.carInfo && (
              <Text style={styles.carInfo}>
                {trip.driver.carInfo.brand} {trip.driver.carInfo.model} · {trip.driver.carInfo.color}
              </Text>
            )}
          </View>
        </View>
      )}

      {/* 价格和操作 */}
      <View style={styles.priceSection}>
        <View style={styles.priceInfo}>
          <Text style={styles.price}>¥{trip.price}</Text>
          <Text style={styles.priceUnit}>/人</Text>
        </View>
        
        <TouchableOpacity
          style={styles.bookButton}
          onPress={() => bookTrip(trip)}
        >
          <Text style={styles.bookButtonText}>预订</Text>
        </TouchableOpacity>
      </View>

      {/* 行程要求 */}
      {trip.requirements && trip.requirements.length > 0 && (
        <View style={styles.requirementsSection}>
          <Text style={styles.requirementsTitle}>要求：</Text>
          <View style={styles.requirementsTags}>
            {trip.requirements.map((requirement, index) => (
              <View key={index} style={styles.requirementTag}>
                <Text style={styles.requirementText}>{requirement}</Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 搜索条件显示 */}
      <View style={styles.searchHeader}>
        <View style={styles.searchRoute}>
          <Text style={styles.searchLocationText}>
            {searchParams.startLocation.name || searchParams.startLocation.address}
          </Text>
          <Text style={styles.searchArrow}>→</Text>
          <Text style={styles.searchLocationText}>
            {searchParams.endLocation.name || searchParams.endLocation.address}
          </Text>
        </View>
        <Text style={styles.searchDate}>{searchParams.departureDate}</Text>
      </View>

      {/* 结果列表 */}
      <ScrollView
        style={styles.resultsList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>搜索中...</Text>
          </View>
        ) : trips.length > 0 ? (
          <>
            <View style={styles.resultsHeader}>
              <Text style={styles.resultsCount}>找到 {trips.length} 个行程</Text>
            </View>
            {trips.map(renderTripCard)}
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>🚗</Text>
            <Text style={styles.emptyTitle}>暂无匹配的行程</Text>
            <Text style={styles.emptyDesc}>试试调整搜索条件或发布行程需求</Text>
            <TouchableOpacity
              style={styles.publishButton}
              onPress={() => navigation.navigate('PublishTrip')}
            >
              <Text style={styles.publishButtonText}>发布行程需求</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  searchHeader: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  searchRoute: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  searchLocationText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  searchArrow: {
    fontSize: 16,
    color: '#999',
    marginHorizontal: 12,
  },
  searchDate: {
    fontSize: 14,
    color: '#666',
  },
  resultsList: {
    flex: 1,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  resultsCount: {
    fontSize: 14,
    color: '#666',
  },
  tripCard: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  routeInfo: {
    marginBottom: 16,
  },
  routeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  startLocation: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  routeArrow: {
    marginHorizontal: 12,
  },
  arrowText: {
    fontSize: 16,
    color: '#999',
  },
  endLocation: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'right',
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  departureTime: {
    fontSize: 14,
    color: '#666',
  },
  availableSeats: {
    fontSize: 14,
    color: '#52c41a',
    fontWeight: '500',
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  driverAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  driverAvatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  driverDetails: {
    flex: 1,
  },
  driverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
  },
  verifiedBadge: {
    backgroundColor: '#52c41a',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  verifiedText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#333',
    marginRight: 2,
  },
  ratingStar: {
    fontSize: 14,
  },
  carInfo: {
    fontSize: 14,
    color: '#666',
  },
  priceSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  priceInfo: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff4d4f',
  },
  priceUnit: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  bookButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  bookButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  requirementsSection: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  requirementsTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  requirementsTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  requirementTag: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  requirementText: {
    fontSize: 12,
    color: '#1890ff',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptyDesc: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  publishButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  publishButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SearchResultScreen;
