import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';
import { Order } from '../types/trip';
import { apiService } from '../services/api';

const OrdersScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'confirmed' | 'completed'>('all');
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadOrders();
  }, [activeTab]);

  // 加载订单列表
  const loadOrders = async () => {
    try {
      setLoading(true);
      const status = activeTab === 'all' ? undefined : activeTab;
      const response = await apiService.getOrders(status);
      
      if (response.success && response.data) {
        setOrders(response.data);
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '加载订单失败');
    } finally {
      setLoading(false);
    }
  };

  // 下拉刷新
  const onRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  // 取消订单
  const cancelOrder = async (orderId: string) => {
    Alert.alert(
      '确认取消',
      '确定要取消这个订单吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await apiService.cancelOrder(orderId);
              if (response.success) {
                Alert.alert('成功', '订单已取消');
                loadOrders();
              } else {
                Alert.alert('失败', response.message || '取消失败');
              }
            } catch (error: any) {
              Alert.alert('错误', error.message || '取消失败');
            }
          },
        },
      ]
    );
  };

  // 确认订单
  const confirmOrder = async (orderId: string) => {
    try {
      const response = await apiService.confirmOrder(orderId);
      if (response.success) {
        Alert.alert('成功', '订单已确认');
        loadOrders();
      } else {
        Alert.alert('失败', response.message || '确认失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '确认失败');
    }
  };

  // 完成订单
  const completeOrder = async (orderId: string) => {
    try {
      const response = await apiService.completeOrder(orderId);
      if (response.success) {
        Alert.alert('成功', '订单已完成');
        loadOrders();
      } else {
        Alert.alert('失败', response.message || '完成失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '完成失败');
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '待确认',
      confirmed: '已确认',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    const styleMap = {
      pending: styles.statusPending,
      confirmed: styles.statusConfirmed,
      in_progress: styles.statusInProgress,
      completed: styles.statusCompleted,
      cancelled: styles.statusCancelled,
    };
    return styleMap[status] || styles.statusDefault;
  };

  // 渲染订单卡片
  const renderOrderCard = (order: Order) => (
    <View key={order.id} style={styles.orderCard}>
      {/* 订单头部 */}
      <View style={styles.orderHeader}>
        <View style={styles.routeInfo}>
          <Text style={styles.startLocation}>
            {order.trip.startLocation.name || order.trip.startLocation.address}
          </Text>
          <Text style={styles.routeArrow}>→</Text>
          <Text style={styles.endLocation}>
            {order.trip.endLocation.name || order.trip.endLocation.address}
          </Text>
        </View>
        <View style={[styles.statusBadge, getStatusStyle(order.status)]}>
          <Text style={styles.statusText}>{getStatusText(order.status)}</Text>
        </View>
      </View>

      {/* 订单详情 */}
      <View style={styles.orderDetails}>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>出发时间</Text>
          <Text style={styles.detailValue}>{formatTime(order.trip.departureTime)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>座位数</Text>
          <Text style={styles.detailValue}>{order.seats}人</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>总价</Text>
          <Text style={[styles.detailValue, styles.priceText]}>¥{order.totalPrice}</Text>
        </View>
      </View>

      {/* 司机信息 */}
      <View style={styles.driverInfo}>
        <View style={styles.driverAvatar}>
          <Text style={styles.driverAvatarText}>
            {order.driver.name.charAt(0)}
          </Text>
        </View>
        <View style={styles.driverDetails}>
          <Text style={styles.driverName}>{order.driver.name}</Text>
          {order.driver.carInfo && (
            <Text style={styles.carInfo}>
              {order.driver.carInfo.brand} {order.driver.carInfo.model} · {order.driver.carInfo.color}
            </Text>
          )}
        </View>
        {order.driver.rating && (
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingText}>{order.driver.rating.toFixed(1)}</Text>
            <Text style={styles.ratingStar}>⭐</Text>
          </View>
        )}
      </View>

      {/* 操作按钮 */}
      <View style={styles.orderActions}>
        {order.status === 'pending' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.cancelButton]}
            onPress={() => cancelOrder(order.id)}
          >
            <Text style={styles.cancelButtonText}>取消订单</Text>
          </TouchableOpacity>
        )}

        {order.status === 'confirmed' && (
          <>
            <TouchableOpacity
              style={[styles.actionButton, styles.contactButton]}
              onPress={() => Alert.alert('联系司机', '功能开发中...')}
            >
              <Text style={styles.contactButtonText}>联系司机</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.actionButton, styles.completeButton]}
              onPress={() => completeOrder(order.id)}
            >
              <Text style={styles.completeButtonText}>完成订单</Text>
            </TouchableOpacity>
          </>
        )}

        {order.status === 'completed' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.reviewButton]}
            onPress={() => Alert.alert('评价', '功能开发中...')}
          >
            <Text style={styles.reviewButtonText}>评价</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 标签栏 */}
      <View style={styles.tabBar}>
        {[
          { key: 'all', label: '全部' },
          { key: 'pending', label: '待确认' },
          { key: 'confirmed', label: '已确认' },
          { key: 'completed', label: '已完成' },
        ].map(tab => (
          <TouchableOpacity
            key={tab.key}
            style={[styles.tab, activeTab === tab.key && styles.activeTab]}
            onPress={() => setActiveTab(tab.key as any)}
          >
            <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* 订单列表 */}
      <ScrollView
        style={styles.ordersList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>加载中...</Text>
          </View>
        ) : orders.length > 0 ? (
          orders.map(renderOrderCard)
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyIcon}>📋</Text>
            <Text style={styles.emptyTitle}>暂无订单</Text>
            <Text style={styles.emptyDesc}>快去搜索或发布行程吧</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  ordersList: {
    flex: 1,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  orderCard: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginTop: 12,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  routeInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  startLocation: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  routeArrow: {
    fontSize: 16,
    color: '#999',
    marginHorizontal: 12,
  },
  endLocation: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'right',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusPending: {
    backgroundColor: '#fff7e6',
  },
  statusConfirmed: {
    backgroundColor: '#e6f7ff',
  },
  statusInProgress: {
    backgroundColor: '#f6ffed',
  },
  statusCompleted: {
    backgroundColor: '#f6ffed',
  },
  statusCancelled: {
    backgroundColor: '#fff2f0',
  },
  statusDefault: {
    backgroundColor: '#f5f5f5',
  },
  orderDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  priceText: {
    color: '#ff4d4f',
    fontWeight: '600',
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  driverAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  driverAvatarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  carInfo: {
    fontSize: 14,
    color: '#666',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#333',
    marginRight: 2,
  },
  ratingStar: {
    fontSize: 14,
  },
  orderActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#fff2f0',
    borderWidth: 1,
    borderColor: '#ffccc7',
  },
  cancelButtonText: {
    color: '#ff4d4f',
    fontSize: 14,
    fontWeight: '600',
  },
  contactButton: {
    backgroundColor: '#f0f9ff',
    borderWidth: 1,
    borderColor: '#b3d8ff',
  },
  contactButtonText: {
    color: '#1890ff',
    fontSize: 14,
    fontWeight: '600',
  },
  completeButton: {
    backgroundColor: '#007AFF',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  reviewButton: {
    backgroundColor: '#f6ffed',
    borderWidth: 1,
    borderColor: '#b7eb8f',
  },
  reviewButtonText: {
    color: '#52c41a',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptyDesc: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default OrdersScreen;
