import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { AuthService } from '../../services/AuthService';
import { validatePhone, validatePassword } from '../../utils/validation';
import { CountdownButton } from '../../components/CountdownButton';
import { CustomInput } from '../../components/CustomInput';

interface ForgotPasswordScreenProps {
  navigation: any;
}

export const ForgotPasswordScreen: React.FC<ForgotPasswordScreenProps> = ({ navigation }) => {
  const [step, setStep] = useState<'phone' | 'reset'>(1);
  const [phone, setPhone] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const phoneRef = useRef<TextInput>(null);
  const smsCodeRef = useRef<TextInput>(null);
  const newPasswordRef = useRef<TextInput>(null);
  const confirmPasswordRef = useRef<TextInput>(null);

  // 发送验证码
  const handleSendSms = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    try {
      setLoading(true);
      await AuthService.sendSmsCode(phone, 'reset_password');
      Alert.alert('成功', '验证码已发送');
      return true;
    } catch (error: any) {
      Alert.alert('错误', error.message || '发送验证码失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 验证手机号和验证码
  const handleVerifyPhone = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!smsCode || smsCode.length < 4) {
      Alert.alert('提示', '请输入正确的验证码');
      return;
    }

    try {
      setLoading(true);
      // 这里可以调用验证接口，验证手机号和验证码是否正确
      // 为了演示，我们直接进入下一步
      setStep('reset');
    } catch (error: any) {
      Alert.alert('错误', error.message || '验证失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置密码
  const handleResetPassword = async () => {
    if (!validatePassword(newPassword)) {
      Alert.alert('提示', '密码长度至少6位');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('提示', '两次输入的密码不一致');
      return;
    }

    try {
      setLoading(true);
      await AuthService.resetPassword(phone, smsCode, newPassword);
      Alert.alert('成功', '密码重置成功', [
        {
          text: '确定',
          onPress: () => navigation.navigate('Login'),
        },
      ]);
    } catch (error: any) {
      Alert.alert('错误', error.message || '重置密码失败');
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.form}>
      <Text style={styles.stepTitle}>验证手机号</Text>
      <Text style={styles.stepDescription}>
        请输入您注册时使用的手机号，我们将发送验证码到该手机号
      </Text>

      {/* 手机号输入 */}
      <CustomInput
        ref={phoneRef}
        placeholder="请输入手机号"
        value={phone}
        onChangeText={setPhone}
        keyboardType="phone-pad"
        maxLength={11}
        leftIcon="phone"
      />

      {/* 验证码输入 */}
      <View style={styles.smsContainer}>
        <CustomInput
          ref={smsCodeRef}
          placeholder="请输入验证码"
          value={smsCode}
          onChangeText={setSmsCode}
          keyboardType="number-pad"
          maxLength={6}
          leftIcon="message-circle"
          containerStyle={styles.smsInput}
        />
        <CountdownButton
          onPress={handleSendSms}
          disabled={!validatePhone(phone)}
          style={styles.smsButton}
        />
      </View>

      {/* 下一步按钮 */}
      <TouchableOpacity
        style={[styles.nextButton, loading && styles.nextButtonDisabled]}
        onPress={handleVerifyPhone}
        disabled={loading}
      >
        <Text style={styles.nextButtonText}>
          {loading ? '验证中...' : '下一步'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderResetStep = () => (
    <View style={styles.form}>
      <Text style={styles.stepTitle}>设置新密码</Text>
      <Text style={styles.stepDescription}>
        请为您的账号设置一个新密码
      </Text>

      {/* 新密码输入 */}
      <CustomInput
        ref={newPasswordRef}
        placeholder="请输入新密码（至少6位）"
        value={newPassword}
        onChangeText={setNewPassword}
        secureTextEntry={!showNewPassword}
        leftIcon="lock"
        rightIcon={showNewPassword ? 'eye-off' : 'eye'}
        onRightIconPress={() => setShowNewPassword(!showNewPassword)}
      />

      {/* 确认密码输入 */}
      <CustomInput
        ref={confirmPasswordRef}
        placeholder="请再次输入新密码"
        value={confirmPassword}
        onChangeText={setConfirmPassword}
        secureTextEntry={!showConfirmPassword}
        leftIcon="lock"
        rightIcon={showConfirmPassword ? 'eye-off' : 'eye'}
        onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
      />

      {/* 重置密码按钮 */}
      <TouchableOpacity
        style={[styles.resetButton, loading && styles.resetButtonDisabled]}
        onPress={handleResetPassword}
        disabled={loading}
      >
        <Text style={styles.resetButtonText}>
          {loading ? '重置中...' : '重置密码'}
        </Text>
      </TouchableOpacity>

      {/* 返回上一步 */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Text style={styles.backButtonText}>返回上一步</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>忘记密码</Text>
            <View style={styles.stepIndicator}>
              <View style={[styles.stepDot, styles.stepDotActive]} />
              <View style={styles.stepLine} />
              <View style={[styles.stepDot, step === 'reset' && styles.stepDotActive]} />
            </View>
          </View>

          {step === 'phone' ? renderPhoneStep() : renderResetStep()}

          {/* 返回登录 */}
          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>想起密码了？</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>返回登录</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 20,
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ddd',
  },
  stepDotActive: {
    backgroundColor: '#007AFF',
  },
  stepLine: {
    width: 40,
    height: 2,
    backgroundColor: '#ddd',
    marginHorizontal: 8,
  },
  form: {
    width: '100%',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 20,
  },
  smsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  smsInput: {
    flex: 1,
  },
  smsButton: {
    width: 100,
  },
  nextButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  nextButtonDisabled: {
    backgroundColor: '#ccc',
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  resetButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  resetButtonDisabled: {
    backgroundColor: '#ccc',
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  backButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  backButtonText: {
    color: '#007AFF',
    fontSize: 16,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
  },
  loginText: {
    color: '#666',
    fontSize: 16,
  },
  loginLink: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 4,
  },
});
