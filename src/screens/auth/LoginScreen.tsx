import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { AuthService } from '../../services/AuthService';
import { validatePhone, validatePassword } from '../../utils/validation';
import { CountdownButton } from '../../components/CountdownButton';
import { CustomInput } from '../../components/CustomInput';

interface LoginScreenProps {
  navigation: any;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [loginType, setLoginType] = useState<'password' | 'sms'>('password');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const phoneRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  const smsCodeRef = useRef<TextInput>(null);

  // 发送验证码
  const handleSendSms = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    try {
      setLoading(true);
      await AuthService.sendSmsCode(phone, 'login');
      Alert.alert('成功', '验证码已发送');
      return true;
    } catch (error: any) {
      Alert.alert('错误', error.message || '发送验证码失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 密码登录
  const handlePasswordLogin = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!validatePassword(password)) {
      Alert.alert('提示', '密码长度至少6位');
      return;
    }

    try {
      setLoading(true);
      const result = await AuthService.loginWithPassword(phone, password);
      Alert.alert('成功', '登录成功');
      // 这里可以导航到主页面
      console.log('登录成功:', result);
    } catch (error: any) {
      Alert.alert('错误', error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 验证码登录
  const handleSmsLogin = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!smsCode || smsCode.length < 4) {
      Alert.alert('提示', '请输入正确的验证码');
      return;
    }

    try {
      setLoading(true);
      const result = await AuthService.loginWithSms(phone, smsCode);
      Alert.alert('成功', '登录成功');
      // 这里可以导航到主页面
      console.log('登录成功:', result);
    } catch (error: any) {
      Alert.alert('错误', error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = () => {
    if (loginType === 'password') {
      handlePasswordLogin();
    } else {
      handleSmsLogin();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>欢迎登录</Text>
            <Text style={styles.subtitle}>请输入您的手机号和密码</Text>
          </View>

          <View style={styles.form}>
            {/* 手机号输入 */}
            <CustomInput
              ref={phoneRef}
              placeholder="请输入手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              maxLength={11}
              leftIcon="phone"
            />

            {/* 登录方式切换 */}
            <View style={styles.loginTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.loginTypeButton,
                  loginType === 'password' && styles.loginTypeButtonActive,
                ]}
                onPress={() => setLoginType('password')}
              >
                <Text
                  style={[
                    styles.loginTypeText,
                    loginType === 'password' && styles.loginTypeTextActive,
                  ]}
                >
                  密码登录
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.loginTypeButton,
                  loginType === 'sms' && styles.loginTypeButtonActive,
                ]}
                onPress={() => setLoginType('sms')}
              >
                <Text
                  style={[
                    styles.loginTypeText,
                    loginType === 'sms' && styles.loginTypeTextActive,
                  ]}
                >
                  验证码登录
                </Text>
              </TouchableOpacity>
            </View>

            {/* 密码输入 */}
            {loginType === 'password' && (
              <CustomInput
                ref={passwordRef}
                placeholder="请输入密码"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                leftIcon="lock"
                rightIcon={showPassword ? 'eye-off' : 'eye'}
                onRightIconPress={() => setShowPassword(!showPassword)}
              />
            )}

            {/* 验证码输入 */}
            {loginType === 'sms' && (
              <View style={styles.smsContainer}>
                <CustomInput
                  ref={smsCodeRef}
                  placeholder="请输入验证码"
                  value={smsCode}
                  onChangeText={setSmsCode}
                  keyboardType="number-pad"
                  maxLength={6}
                  leftIcon="message-circle"
                  containerStyle={styles.smsInput}
                />
                <CountdownButton
                  onPress={handleSendSms}
                  disabled={!validatePhone(phone)}
                  style={styles.smsButton}
                />
              </View>
            )}

            {/* 忘记密码 */}
            {loginType === 'password' && (
              <TouchableOpacity
                style={styles.forgotPassword}
                onPress={() => navigation.navigate('ForgotPassword')}
              >
                <Text style={styles.forgotPasswordText}>忘记密码？</Text>
              </TouchableOpacity>
            )}

            {/* 登录按钮 */}
            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={styles.loginButtonText}>
                {loading ? '登录中...' : '登录'}
              </Text>
            </TouchableOpacity>

            {/* 注册链接 */}
            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>还没有账号？</Text>
              <TouchableOpacity onPress={() => navigation.navigate('Register')}>
                <Text style={styles.registerLink}>立即注册</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    width: '100%',
  },
  loginTypeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 4,
    marginBottom: 20,
  },
  loginTypeButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  loginTypeButtonActive: {
    backgroundColor: '#007AFF',
  },
  loginTypeText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  loginTypeTextActive: {
    color: '#fff',
  },
  smsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  smsInput: {
    flex: 1,
  },
  smsButton: {
    width: 100,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginTop: 12,
    marginBottom: 20,
  },
  forgotPasswordText: {
    color: '#007AFF',
    fontSize: 14,
  },
  loginButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
  },
  registerText: {
    color: '#666',
    fontSize: 16,
  },
  registerLink: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 4,
  },
});
