import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { AuthService } from '../../services/AuthService';
import { validatePhone, validatePassword } from '../../utils/validation';
import { CountdownButton } from '../../components/CountdownButton';
import { CustomInput } from '../../components/CustomInput';

interface RegisterScreenProps {
  navigation: any;
}

export const RegisterScreen: React.FC<RegisterScreenProps> = ({ navigation }) => {
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [nickname, setNickname] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const phoneRef = useRef<TextInput>(null);
  const passwordRef = useRef<TextInput>(null);
  const confirmPasswordRef = useRef<TextInput>(null);
  const smsCodeRef = useRef<TextInput>(null);
  const nicknameRef = useRef<TextInput>(null);

  // 发送验证码
  const handleSendSms = async () => {
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    try {
      setLoading(true);
      await AuthService.sendSmsCode(phone, 'register');
      Alert.alert('成功', '验证码已发送');
      return true;
    } catch (error: any) {
      Alert.alert('错误', error.message || '发送验证码失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const handleRegister = async () => {
    // 表单验证
    if (!validatePhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    if (!smsCode || smsCode.length < 4) {
      Alert.alert('提示', '请输入正确的验证码');
      return;
    }

    if (!validatePassword(password)) {
      Alert.alert('提示', '密码长度至少6位');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('提示', '两次输入的密码不一致');
      return;
    }

    if (!nickname.trim()) {
      Alert.alert('提示', '请输入昵称');
      return;
    }

    if (!agreedToTerms) {
      Alert.alert('提示', '请先同意用户协议和隐私政策');
      return;
    }

    try {
      setLoading(true);
      const result = await AuthService.register({
        phone,
        password,
        smsCode,
        nickname: nickname.trim(),
      });
      Alert.alert('成功', '注册成功', [
        {
          text: '确定',
          onPress: () => navigation.navigate('Login'),
        },
      ]);
      console.log('注册成功:', result);
    } catch (error: any) {
      Alert.alert('错误', error.message || '注册失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>创建账号</Text>
            <Text style={styles.subtitle}>请填写以下信息完成注册</Text>
          </View>

          <View style={styles.form}>
            {/* 手机号输入 */}
            <CustomInput
              ref={phoneRef}
              placeholder="请输入手机号"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              maxLength={11}
              leftIcon="phone"
            />

            {/* 验证码输入 */}
            <View style={styles.smsContainer}>
              <CustomInput
                ref={smsCodeRef}
                placeholder="请输入验证码"
                value={smsCode}
                onChangeText={setSmsCode}
                keyboardType="number-pad"
                maxLength={6}
                leftIcon="message-circle"
                containerStyle={styles.smsInput}
              />
              <CountdownButton
                onPress={handleSendSms}
                disabled={!validatePhone(phone)}
                style={styles.smsButton}
              />
            </View>

            {/* 昵称输入 */}
            <CustomInput
              ref={nicknameRef}
              placeholder="请输入昵称"
              value={nickname}
              onChangeText={setNickname}
              maxLength={20}
              leftIcon="user"
            />

            {/* 密码输入 */}
            <CustomInput
              ref={passwordRef}
              placeholder="请设置密码（至少6位）"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              leftIcon="lock"
              rightIcon={showPassword ? 'eye-off' : 'eye'}
              onRightIconPress={() => setShowPassword(!showPassword)}
            />

            {/* 确认密码输入 */}
            <CustomInput
              ref={confirmPasswordRef}
              placeholder="请再次输入密码"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              leftIcon="lock"
              rightIcon={showConfirmPassword ? 'eye-off' : 'eye'}
              onRightIconPress={() => setShowConfirmPassword(!showConfirmPassword)}
            />

            {/* 用户协议 */}
            <View style={styles.termsContainer}>
              <TouchableOpacity
                style={styles.checkbox}
                onPress={() => setAgreedToTerms(!agreedToTerms)}
              >
                <View style={[styles.checkboxInner, agreedToTerms && styles.checkboxChecked]}>
                  {agreedToTerms && <Text style={styles.checkmark}>✓</Text>}
                </View>
              </TouchableOpacity>
              <View style={styles.termsTextContainer}>
                <Text style={styles.termsText}>我已阅读并同意</Text>
                <TouchableOpacity>
                  <Text style={styles.termsLink}>《用户协议》</Text>
                </TouchableOpacity>
                <Text style={styles.termsText}>和</Text>
                <TouchableOpacity>
                  <Text style={styles.termsLink}>《隐私政策》</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* 注册按钮 */}
            <TouchableOpacity
              style={[styles.registerButton, loading && styles.registerButtonDisabled]}
              onPress={handleRegister}
              disabled={loading}
            >
              <Text style={styles.registerButtonText}>
                {loading ? '注册中...' : '注册'}
              </Text>
            </TouchableOpacity>

            {/* 登录链接 */}
            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>已有账号？</Text>
              <TouchableOpacity onPress={() => navigation.navigate('Login')}>
                <Text style={styles.loginLink}>立即登录</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    width: '100%',
  },
  smsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  smsInput: {
    flex: 1,
  },
  smsButton: {
    width: 100,
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 20,
    marginBottom: 30,
  },
  checkbox: {
    marginRight: 8,
    marginTop: 2,
  },
  checkboxInner: {
    width: 18,
    height: 18,
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  checkmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  termsTextContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  termsText: {
    fontSize: 14,
    color: '#666',
  },
  termsLink: {
    fontSize: 14,
    color: '#007AFF',
    marginHorizontal: 2,
  },
  registerButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  registerButtonDisabled: {
    backgroundColor: '#ccc',
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
  },
  loginText: {
    color: '#666',
    fontSize: 16,
  },
  loginLink: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 4,
  },
});
