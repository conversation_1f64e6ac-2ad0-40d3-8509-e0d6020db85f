import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { AuthService } from '../../services/AuthService';
import Icon from 'react-native-vector-icons/Feather';

interface UserProfile {
  id: number;
  name: string;
  phone: string;
  email?: string;
  avatar?: string;
  gender?: string;
  role: string;
  phoneVerified: boolean;
  wechatOpenId?: string;
  status: string;
  lastLoginAt: string;
  createdAt: string;
}

export const ProfileScreen: React.FC = () => {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await AuthService.getUserProfile();
      if (response.success && response.data) {
        setProfile(response.data);
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleEditProfile = () => {
    Alert.alert('提示', '编辑功能开发中...');
  };

  const handleChangePassword = () => {
    Alert.alert('提示', '修改密码功能开发中...');
  };

  const handleBindPhone = () => {
    Alert.alert('提示', '绑定手机号功能开发中...');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>获取用户信息失败</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadProfile}>
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* 用户头像和基本信息 */}
        <View style={styles.profileHeader}>
          <View style={styles.avatar}>
            <Icon name="user" size={40} color="#007AFF" />
          </View>
          <Text style={styles.name}>{profile.name}</Text>
          <Text style={styles.phone}>{profile.phone}</Text>
          {profile.phoneVerified && (
            <View style={styles.verifiedBadge}>
              <Icon name="check-circle" size={16} color="#52c41a" />
              <Text style={styles.verifiedText}>已验证</Text>
            </View>
          )}
        </View>

        {/* 个人信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>个人信息</Text>
          
          <View style={styles.infoItem}>
            <Icon name="user" size={20} color="#666" />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>昵称</Text>
              <Text style={styles.infoValue}>{profile.name}</Text>
            </View>
            <TouchableOpacity onPress={handleEditProfile}>
              <Icon name="chevron-right" size={20} color="#ccc" />
            </TouchableOpacity>
          </View>

          <View style={styles.infoItem}>
            <Icon name="phone" size={20} color="#666" />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>手机号</Text>
              <Text style={styles.infoValue}>{profile.phone}</Text>
            </View>
            <TouchableOpacity onPress={handleBindPhone}>
              <Icon name="chevron-right" size={20} color="#ccc" />
            </TouchableOpacity>
          </View>

          {profile.email && (
            <View style={styles.infoItem}>
              <Icon name="mail" size={20} color="#666" />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>邮箱</Text>
                <Text style={styles.infoValue}>{profile.email}</Text>
              </View>
              <TouchableOpacity>
                <Icon name="chevron-right" size={20} color="#ccc" />
              </TouchableOpacity>
            </View>
          )}

          <View style={styles.infoItem}>
            <Icon name="calendar" size={20} color="#666" />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>注册时间</Text>
              <Text style={styles.infoValue}>
                {new Date(profile.createdAt).toLocaleDateString()}
              </Text>
            </View>
          </View>

          <View style={styles.infoItem}>
            <Icon name="clock" size={20} color="#666" />
            <View style={styles.infoContent}>
              <Text style={styles.infoLabel}>最后登录</Text>
              <Text style={styles.infoValue}>
                {new Date(profile.lastLoginAt).toLocaleString()}
              </Text>
            </View>
          </View>
        </View>

        {/* 账户安全 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>账户安全</Text>
          
          <TouchableOpacity style={styles.actionItem} onPress={handleChangePassword}>
            <Icon name="lock" size={20} color="#666" />
            <Text style={styles.actionText}>修改密码</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <Icon name="shield" size={20} color="#666" />
            <Text style={styles.actionText}>安全设置</Text>
            <Icon name="chevron-right" size={20} color="#ccc" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  profileHeader: {
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingVertical: 30,
    marginBottom: 20,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  name: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  phone: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f6ffed',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  verifiedText: {
    fontSize: 12,
    color: '#52c41a',
    marginLeft: 4,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    color: '#1a1a1a',
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#1a1a1a',
    marginLeft: 12,
  },
});
