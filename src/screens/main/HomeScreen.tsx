import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { AuthService } from '../../services/AuthService';
import Icon from 'react-native-vector-icons/Feather';

interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const handleLogout = async () => {
    try {
      await AuthService.logout();
      // 导航会自动处理，因为RootNavigator会检测到登录状态变化
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  const handleProfile = () => {
    navigation.navigate('Profile');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>欢迎使用</Text>
          <Text style={styles.subtitle}>您已成功登录</Text>
        </View>

        <View style={styles.content}>
          <View style={styles.card}>
            <Icon name="user" size={24} color="#007AFF" />
            <Text style={styles.cardTitle}>个人中心</Text>
            <Text style={styles.cardDescription}>查看和编辑个人信息</Text>
            <TouchableOpacity style={styles.cardButton} onPress={handleProfile}>
              <Text style={styles.cardButtonText}>进入</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.card}>
            <Icon name="settings" size={24} color="#007AFF" />
            <Text style={styles.cardTitle}>设置</Text>
            <Text style={styles.cardDescription}>应用设置和偏好</Text>
            <TouchableOpacity style={styles.cardButton}>
              <Text style={styles.cardButtonText}>进入</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.card}>
            <Icon name="help-circle" size={24} color="#007AFF" />
            <Text style={styles.cardTitle}>帮助</Text>
            <Text style={styles.cardDescription}>常见问题和支持</Text>
            <TouchableOpacity style={styles.cardButton}>
              <Text style={styles.cardButtonText}>进入</Text>
            </TouchableOpacity>
          </View>
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Icon name="log-out" size={20} color="#fff" />
          <Text style={styles.logoutButtonText}>退出登录</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginTop: 12,
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
  },
  cardButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cardButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  logoutButton: {
    flexDirection: 'row',
    backgroundColor: '#ff4757',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
