import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Switch,
} from 'react-native';
import { Location, PublishTripParams, CarInfo } from '../types/trip';
import { apiService } from '../services/api';

interface PublishTripState {
  tripType: 'driver' | 'passenger';
  startLocation: Location | null;
  endLocation: Location | null;
  departureDate: string;
  departureTime: string;
  seats: number;
  price: number;
  description: string;
  requirements: string[];
  carInfo: CarInfo | null;
  loading: boolean;
}

const PublishTripScreen: React.FC = () => {
  const [state, setState] = useState<PublishTripState>({
    tripType: 'driver',
    startLocation: null,
    endLocation: null,
    departureDate: new Date().toISOString().split('T')[0],
    departureTime: '08:00',
    seats: 1,
    price: 0,
    description: '',
    requirements: [],
    carInfo: null,
    loading: false,
  });

  const requirementOptions = [
    '不吸烟', '不带宠物', '准时出发', '可聊天', '安静出行', '女性优先'
  ];

  // 切换行程类型
  const switchTripType = (type: 'driver' | 'passenger') => {
    setState(prev => ({ ...prev, tripType: type }));
  };

  // 选择位置
  const selectLocation = (type: 'start' | 'end') => {
    // 这里应该打开地图选择页面或地址搜索页面
    Alert.alert('选择位置', `请选择${type === 'start' ? '起点' : '终点'}`);
  };

  // 切换要求
  const toggleRequirement = (requirement: string) => {
    setState(prev => ({
      ...prev,
      requirements: prev.requirements.includes(requirement)
        ? prev.requirements.filter(r => r !== requirement)
        : [...prev.requirements, requirement],
    }));
  };

  // 发布行程
  const publishTrip = async () => {
    // 表单验证
    if (!state.startLocation || !state.endLocation) {
      Alert.alert('提示', '请选择起点和终点');
      return;
    }

    if (state.price <= 0) {
      Alert.alert('提示', '请输入正确的价格');
      return;
    }

    if (state.tripType === 'driver' && !state.carInfo) {
      Alert.alert('提示', '请填写车辆信息');
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true }));

      const params: PublishTripParams = {
        type: state.tripType,
        startLocation: state.startLocation,
        endLocation: state.endLocation,
        departureTime: `${state.departureDate} ${state.departureTime}`,
        seats: state.seats,
        price: state.price,
        description: state.description,
        requirements: state.requirements,
        ...(state.carInfo && { carInfo: state.carInfo }),
      };

      const response = await apiService.publishTrip(params);

      if (response.success) {
        Alert.alert('成功', '行程发布成功！', [
          {
            text: '确定',
            onPress: () => {
              // 导航回首页或行程详情页
            },
          },
        ]);
      } else {
        throw new Error(response.message || '发布失败');
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '发布失败，请重试');
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 行程类型选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>我要</Text>
          <View style={styles.tripTypeContainer}>
            <TouchableOpacity
              style={[
                styles.tripTypeButton,
                state.tripType === 'driver' && styles.tripTypeButtonActive,
              ]}
              onPress={() => switchTripType('driver')}
            >
              <Text style={styles.tripTypeIcon}>🚗</Text>
              <Text
                style={[
                  styles.tripTypeText,
                  state.tripType === 'driver' && styles.tripTypeTextActive,
                ]}
              >
                车找人
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.tripTypeButton,
                state.tripType === 'passenger' && styles.tripTypeButtonActive,
              ]}
              onPress={() => switchTripType('passenger')}
            >
              <Text style={styles.tripTypeIcon}>🙋</Text>
              <Text
                style={[
                  styles.tripTypeText,
                  state.tripType === 'passenger' && styles.tripTypeTextActive,
                ]}
              >
                人找车
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 路线信息 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>路线信息</Text>
          <View style={styles.routeContainer}>
            <TouchableOpacity
              style={styles.locationButton}
              onPress={() => selectLocation('start')}
            >
              <View style={[styles.locationDot, { backgroundColor: '#52c41a' }]} />
              <View style={styles.locationContent}>
                <Text style={styles.locationLabel}>起点</Text>
                <Text style={styles.locationText}>
                  {state.startLocation?.name || state.startLocation?.address || '请选择起点'}
                </Text>
              </View>
            </TouchableOpacity>

            <View style={styles.routeDivider} />

            <TouchableOpacity
              style={styles.locationButton}
              onPress={() => selectLocation('end')}
            >
              <View style={[styles.locationDot, { backgroundColor: '#ff4d4f' }]} />
              <View style={styles.locationContent}>
                <Text style={styles.locationLabel}>终点</Text>
                <Text style={styles.locationText}>
                  {state.endLocation?.name || state.endLocation?.address || '请选择终点'}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* 出发时间 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>出发时间</Text>
          <View style={styles.timeContainer}>
            <View style={styles.timeInputContainer}>
              <Text style={styles.timeLabel}>日期</Text>
              <TextInput
                style={styles.timeInput}
                value={state.departureDate}
                onChangeText={(text) => setState(prev => ({ ...prev, departureDate: text }))}
                placeholder="YYYY-MM-DD"
              />
            </View>
            
            <View style={styles.timeInputContainer}>
              <Text style={styles.timeLabel}>时间</Text>
              <TextInput
                style={styles.timeInput}
                value={state.departureTime}
                onChangeText={(text) => setState(prev => ({ ...prev, departureTime: text }))}
                placeholder="HH:MM"
              />
            </View>
          </View>
        </View>

        {/* 座位和价格 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>详细信息</Text>
          <View style={styles.detailsContainer}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>
                {state.tripType === 'driver' ? '可载人数' : '需要座位'}
              </Text>
              <View style={styles.seatSelector}>
                <TouchableOpacity
                  style={styles.seatButton}
                  onPress={() => setState(prev => ({ ...prev, seats: Math.max(1, prev.seats - 1) }))}
                >
                  <Text style={styles.seatButtonText}>-</Text>
                </TouchableOpacity>
                <Text style={styles.seatCount}>{state.seats}</Text>
                <TouchableOpacity
                  style={styles.seatButton}
                  onPress={() => setState(prev => ({ ...prev, seats: Math.min(6, prev.seats + 1) }))}
                >
                  <Text style={styles.seatButtonText}>+</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>价格 (元/人)</Text>
              <TextInput
                style={styles.priceInput}
                value={state.price.toString()}
                onChangeText={(text) => setState(prev => ({ ...prev, price: Number(text) || 0 }))}
                keyboardType="numeric"
                placeholder="0"
              />
            </View>
          </View>
        </View>

        {/* 车辆信息 (仅车主) */}
        {state.tripType === 'driver' && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>车辆信息</Text>
            <View style={styles.carInfoContainer}>
              <TextInput
                style={styles.carInput}
                placeholder="车辆品牌"
                value={state.carInfo?.brand || ''}
                onChangeText={(text) => setState(prev => ({
                  ...prev,
                  carInfo: { ...prev.carInfo, brand: text } as CarInfo
                }))}
              />
              <TextInput
                style={styles.carInput}
                placeholder="车辆型号"
                value={state.carInfo?.model || ''}
                onChangeText={(text) => setState(prev => ({
                  ...prev,
                  carInfo: { ...prev.carInfo, model: text } as CarInfo
                }))}
              />
              <TextInput
                style={styles.carInput}
                placeholder="车辆颜色"
                value={state.carInfo?.color || ''}
                onChangeText={(text) => setState(prev => ({
                  ...prev,
                  carInfo: { ...prev.carInfo, color: text } as CarInfo
                }))}
              />
              <TextInput
                style={styles.carInput}
                placeholder="车牌号"
                value={state.carInfo?.plateNumber || ''}
                onChangeText={(text) => setState(prev => ({
                  ...prev,
                  carInfo: { ...prev.carInfo, plateNumber: text } as CarInfo
                }))}
              />
            </View>
          </View>
        )}

        {/* 出行要求 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>出行要求</Text>
          <View style={styles.requirementsContainer}>
            {requirementOptions.map(requirement => (
              <TouchableOpacity
                key={requirement}
                style={[
                  styles.requirementTag,
                  state.requirements.includes(requirement) && styles.requirementTagActive,
                ]}
                onPress={() => toggleRequirement(requirement)}
              >
                <Text
                  style={[
                    styles.requirementText,
                    state.requirements.includes(requirement) && styles.requirementTextActive,
                  ]}
                >
                  {requirement}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 备注说明 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>备注说明</Text>
          <TextInput
            style={styles.descriptionInput}
            placeholder="请输入备注说明（选填）"
            value={state.description}
            onChangeText={(text) => setState(prev => ({ ...prev, description: text }))}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* 发布按钮 */}
        <TouchableOpacity
          style={[styles.publishButton, state.loading && styles.publishButtonDisabled]}
          onPress={publishTrip}
          disabled={state.loading}
        >
          <Text style={styles.publishButtonText}>
            {state.loading ? '发布中...' : '发布行程'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 12,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  tripTypeContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 4,
  },
  tripTypeButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    borderRadius: 8,
  },
  tripTypeButtonActive: {
    backgroundColor: '#007AFF',
  },
  tripTypeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  tripTypeText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666',
  },
  tripTypeTextActive: {
    color: '#fff',
  },
  routeContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  locationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 16,
  },
  locationContent: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 16,
    color: '#333',
  },
  routeDivider: {
    width: 2,
    height: 20,
    backgroundColor: '#ddd',
    marginLeft: 22,
    marginVertical: 8,
  },
  timeContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  timeInputContainer: {
    flex: 1,
  },
  timeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  timeInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  detailsContainer: {
    gap: 20,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  detailLabel: {
    fontSize: 16,
    color: '#333',
  },
  seatSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 4,
  },
  seatButton: {
    width: 32,
    height: 32,
    backgroundColor: '#007AFF',
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  seatButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  seatCount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 20,
  },
  priceInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    minWidth: 100,
    textAlign: 'right',
  },
  carInfoContainer: {
    gap: 12,
  },
  carInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  requirementsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  requirementTag: {
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#e8e8e8',
  },
  requirementTagActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  requirementText: {
    fontSize: 14,
    color: '#666',
  },
  requirementTextActive: {
    color: '#fff',
  },
  descriptionInput: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    minHeight: 100,
  },
  publishButton: {
    backgroundColor: '#007AFF',
    marginHorizontal: 16,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  publishButtonDisabled: {
    backgroundColor: '#ccc',
  },
  publishButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default PublishTripScreen;
