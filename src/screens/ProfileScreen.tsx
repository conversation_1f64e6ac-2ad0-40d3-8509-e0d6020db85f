import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Alert,
  Image,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { User, Statistics } from '../types/trip';
import { apiService } from '../services/api';

const ProfileScreen: React.FC = ({ navigation }: any) => {
  const { user, logout } = useAuth();
  const [stats, setStats] = useState<Statistics | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadUserStats();
  }, []);

  // 加载用户统计数据
  const loadUserStats = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取用户统计数据的API
      // const response = await apiService.getUserStats();
      // if (response.success) {
      //   setStats(response.data);
      // }
      
      // 模拟数据
      setStats({
        totalTrips: 12,
        completedTrips: 10,
        rating: 4.8,
        totalDistance: 1250,
        carbonSaved: 85.5,
      });
    } catch (error: any) {
      console.error('加载统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 退出登录
  const handleLogout = () => {
    Alert.alert('确认退出', '您确定要退出登录吗？', [
      { text: '取消', style: 'cancel' },
      {
        text: '确定',
        style: 'destructive',
        onPress: logout,
      },
    ]);
  };

  // 菜单项数据
  const menuItems = [
    {
      icon: '🚗',
      title: '我的行程',
      subtitle: '查看发布的行程',
      onPress: () => navigation.navigate('MyTrips'),
    },
    {
      icon: '🚙',
      title: '我的车辆',
      subtitle: '管理车辆信息',
      onPress: () => navigation.navigate('MyCars'),
    },
    {
      icon: '💰',
      title: '我的钱包',
      subtitle: '余额和交易记录',
      onPress: () => navigation.navigate('Wallet'),
    },
    {
      icon: '🛡️',
      title: '安全中心',
      subtitle: '安全设置和紧急联系',
      onPress: () => navigation.navigate('Safety'),
    },
    {
      icon: '💬',
      title: '客服中心',
      subtitle: '帮助和反馈',
      onPress: () => navigation.navigate('CustomerService'),
    },
    {
      icon: '⚙️',
      title: '设置',
      subtitle: '账户和隐私设置',
      onPress: () => navigation.navigate('Settings'),
    },
  ];

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loginPrompt}>
          <Text style={styles.loginPromptIcon}>👤</Text>
          <Text style={styles.loginPromptText}>请先登录</Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.loginButtonText}>去登录</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 用户信息头部 */}
        <View style={styles.userHeader}>
          <TouchableOpacity
            style={styles.userInfo}
            onPress={() => navigation.navigate('EditProfile')}
          >
            <View style={styles.avatarContainer}>
              {user.avatar ? (
                <Image source={{ uri: user.avatar }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Text style={styles.avatarText}>{user.name?.charAt(0) || '?'}</Text>
                </View>
              )}
              {user.verified && (
                <View style={styles.verifiedBadge}>
                  <Text style={styles.verifiedText}>✓</Text>
                </View>
              )}
            </View>

            <View style={styles.userDetails}>
              <Text style={styles.userName}>{user.name}</Text>
              <Text style={styles.userPhone}>{user.phone}</Text>
              {user.rating && (
                <View style={styles.ratingContainer}>
                  <Text style={styles.ratingText}>{user.rating.toFixed(1)}</Text>
                  <Text style={styles.ratingStar}>⭐</Text>
                </View>
              )}
            </View>

            <Text style={styles.editIcon}>></Text>
          </TouchableOpacity>

          {/* 统计信息 */}
          {stats && (
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.totalTrips}</Text>
                <Text style={styles.statLabel}>行程</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.completedTrips}</Text>
                <Text style={styles.statLabel}>完成</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.totalDistance}km</Text>
                <Text style={styles.statLabel}>里程</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{stats.carbonSaved}kg</Text>
                <Text style={styles.statLabel}>减碳</Text>
              </View>
            </View>
          )}
        </View>

        {/* 功能菜单 */}
        <View style={styles.menuContainer}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={item.onPress}
            >
              <View style={styles.menuIcon}>
                <Text style={styles.menuIconText}>{item.icon}</Text>
              </View>
              <View style={styles.menuContent}>
                <Text style={styles.menuTitle}>{item.title}</Text>
                <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
              </View>
              <Text style={styles.menuArrow}>></Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* 关于我们 */}
        <View style={styles.aboutSection}>
          <TouchableOpacity
            style={styles.aboutItem}
            onPress={() => navigation.navigate('About')}
          >
            <Text style={styles.aboutText}>关于白菜出行</Text>
            <Text style={styles.menuArrow}>></Text>
          </TouchableOpacity>
        </View>

        {/* 退出登录 */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>退出登录</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  loginPrompt: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loginPromptIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  loginPromptText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 30,
  },
  loginButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  userHeader: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 20,
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '600',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#52c41a',
    alignItems: 'center',
    justifyContent: 'center',
  },
  verifiedText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#333',
    marginRight: 2,
  },
  ratingStar: {
    fontSize: 14,
  },
  editIcon: {
    fontSize: 16,
    color: '#999',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#e8e8e8',
    marginHorizontal: 16,
  },
  menuContainer: {
    backgroundColor: '#fff',
    marginBottom: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  menuIconText: {
    fontSize: 20,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 12,
    color: '#666',
  },
  menuArrow: {
    fontSize: 16,
    color: '#999',
  },
  aboutSection: {
    backgroundColor: '#fff',
    marginBottom: 12,
  },
  aboutItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  aboutText: {
    fontSize: 16,
    color: '#333',
  },
  logoutButton: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ff4d4f',
  },
  logoutButtonText: {
    color: '#ff4d4f',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ProfileScreen;
