import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Chat, Message, User } from '../types/trip';
import { apiService } from '../services/api';
import { formatTime } from '../utils/tripHelpers';

interface ChatScreenProps {
  route: {
    params: {
      chatId?: string;
      userId?: string;
      orderId?: string;
    };
  };
  navigation: any;
}

const ChatScreen: React.FC<ChatScreenProps> = ({ route, navigation }) => {
  const { chatId, userId, orderId } = route.params;
  const [chat, setChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (chatId) {
      loadChat();
      loadMessages();
    } else if (userId) {
      // 创建新的聊天
      createChat();
    }
  }, [chatId, userId]);

  // 加载聊天信息
  const loadChat = async () => {
    if (!chatId) return;
    
    try {
      const response = await apiService.getChatDetail(chatId);
      if (response.success && response.data) {
        setChat(response.data);
        navigation.setOptions({
          title: getOtherUser(response.data)?.name || '聊天',
        });
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '加载聊天失败');
    }
  };

  // 加载消息列表
  const loadMessages = async () => {
    if (!chatId) return;
    
    try {
      setLoading(true);
      const response = await apiService.getMessages(chatId);
      if (response.success && response.data) {
        setMessages(response.data);
        // 标记消息为已读
        await apiService.markAsRead(chatId);
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '加载消息失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建新聊天
  const createChat = async () => {
    // 这里应该调用创建聊天的API
    Alert.alert('提示', '创建聊天功能开发中...');
  };

  // 发送消息
  const sendMessage = async () => {
    if (!inputText.trim() || !chatId || sending) return;

    const messageText = inputText.trim();
    setInputText('');
    setSending(true);

    try {
      const response = await apiService.sendMessage(chatId, messageText);
      if (response.success && response.data) {
        setMessages(prev => [...prev, response.data]);
        // 滚动到底部
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error: any) {
      Alert.alert('错误', error.message || '发送失败');
      setInputText(messageText); // 恢复输入内容
    } finally {
      setSending(false);
    }
  };

  // 获取对方用户信息
  const getOtherUser = (chat: Chat): User | null => {
    // 这里应该根据当前用户ID过滤出对方用户
    return chat.participants[0] || null;
  };

  // 判断是否是自己的消息
  const isMyMessage = (message: Message): boolean => {
    // 这里应该根据当前用户ID判断
    return message.senderId === 'current_user_id';
  };

  // 渲染消息气泡
  const renderMessage = (message: Message, index: number) => {
    const isMy = isMyMessage(message);
    const showTime = index === 0 || 
      new Date(message.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000; // 5分钟

    return (
      <View key={message.id} style={styles.messageContainer}>
        {showTime && (
          <Text style={styles.messageTime}>
            {formatTime(message.timestamp, 'time')}
          </Text>
        )}
        <View style={[
          styles.messageBubble,
          isMy ? styles.myMessageBubble : styles.otherMessageBubble,
        ]}>
          <Text style={[
            styles.messageText,
            isMy ? styles.myMessageText : styles.otherMessageText,
          ]}>
            {message.content}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 消息列表 */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
        >
          {loading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>加载中...</Text>
            </View>
          ) : messages.length > 0 ? (
            messages.map(renderMessage)
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>暂无消息</Text>
              <Text style={styles.emptyDesc}>开始聊天吧</Text>
            </View>
          )}
        </ScrollView>

        {/* 输入框 */}
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="输入消息..."
            multiline
            maxLength={500}
            onSubmitEditing={sendMessage}
            blurOnSubmit={false}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!inputText.trim() || sending) && styles.sendButtonDisabled,
            ]}
            onPress={sendMessage}
            disabled={!inputText.trim() || sending}
          >
            <Text style={[
              styles.sendButtonText,
              (!inputText.trim() || sending) && styles.sendButtonTextDisabled,
            ]}>
              {sending ? '发送中' : '发送'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* 快捷操作 */}
        {orderId && (
          <View style={styles.quickActions}>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => Alert.alert('位置分享', '功能开发中...')}
            >
              <Text style={styles.quickActionText}>📍 分享位置</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.quickActionButton}
              onPress={() => Alert.alert('订单详情', '功能开发中...')}
            >
              <Text style={styles.quickActionText}>📋 订单详情</Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  emptyDesc: {
    fontSize: 14,
    color: '#666',
  },
  messageContainer: {
    marginBottom: 16,
  },
  messageTime: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginBottom: 8,
  },
  messageBubble: {
    maxWidth: '80%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
  },
  myMessageBubble: {
    backgroundColor: '#007AFF',
    alignSelf: 'flex-end',
    borderBottomRightRadius: 8,
  },
  otherMessageBubble: {
    backgroundColor: '#fff',
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  myMessageText: {
    color: '#fff',
  },
  otherMessageText: {
    color: '#333',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  textInput: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 12,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  sendButtonTextDisabled: {
    color: '#999',
  },
  quickActions: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: 14,
    color: '#666',
  },
});

export default ChatScreen;
