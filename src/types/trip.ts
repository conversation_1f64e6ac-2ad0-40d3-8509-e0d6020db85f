// 位置信息
export interface Location {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
  city?: string;
  district?: string;
}

// 地图标记
export interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  iconPath: string;
  width: number;
  height: number;
  title?: string;
}

// 用户信息
export interface User {
  id: string;
  phone: string;
  name: string;
  avatar?: string;
  gender?: 'male' | 'female';
  age?: number;
  rating?: number;
  reviewCount?: number;
  isVerified?: boolean;
  carInfo?: CarInfo;
  createdAt: string;
  updatedAt: string;
}

// 车辆信息
export interface CarInfo {
  brand: string;
  model: string;
  color: string;
  plateNumber: string;
  year?: number;
  seats?: number;
}

// 行程状态
export type TripStatus = 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';

// 订单状态
export type OrderStatus = 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';

// 行程类型
export type TripType = 'driver' | 'passenger';

// 行程信息
export interface Trip {
  id: string;
  type: TripType;
  status: TripStatus;
  driver: User;
  passengers: User[];
  startLocation: Location;
  endLocation: Location;
  departureTime: string;
  price: number;
  seats: number;
  availableSeats: number;
  description?: string;
  requirements?: string[];
  distance?: number;
  duration?: number;
  createdAt: string;
  updatedAt: string;
}

// 订单信息
export interface Order {
  id: string;
  tripId: string;
  trip: Trip;
  passenger: User;
  driver: User;
  status: OrderStatus;
  seats: number;
  totalPrice: number;
  paymentStatus?: 'pending' | 'paid' | 'refunded';
  createdAt: string;
  updatedAt: string;
}

// 聊天消息
export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'location';
  timestamp: string;
  isRead: boolean;
}

// 聊天会话
export interface Chat {
  id: string;
  orderId: string;
  participants: User[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

// 搜索参数
export interface SearchParams {
  startLocation: Location;
  endLocation: Location;
  departureDate?: string;
  seats?: number;
  priceRange?: {
    min: number;
    max: number;
  };
}

// 发布行程参数
export interface PublishTripParams {
  type: TripType;
  startLocation: Location | null;
  endLocation: Location | null;
  departureTime: string;
  seats: number;
  price: number;
  description?: string;
  requirements?: string[];
  carInfo?: CarInfo;
}

// API响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: number;
}

// 统计信息
export interface Statistics {
  totalTrips: number;
  completedTrips: number;
  totalDistance: number;
  totalSavings: number;
  rating: number;
  reviewCount: number;
}

// 认证信息
export interface AuthInfo {
  token: string;
  user: User;
  expiresAt: string;
}

// 登录参数
export interface LoginParams {
  phone: string;
  password?: string;
  smsCode?: string;
  type: 'password' | 'sms';
}

// 注册参数
export interface RegisterParams {
  phone: string;
  smsCode: string;
  password: string;
  name: string;
}

// 地址簡化信息
export interface AddressInfo {
  name: string;
  address: string;
  location: Location;
  type?: 'home' | 'company' | 'other';
}

// 快捷服务
export interface QuickService {
  id: string;
  name: string;
  icon: string;
  description?: string;
  url?: string;
}

// 活动信息
export interface Activity {
  id: string;
  title: string;
  description: string;
  image: string;
  url?: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
}
