import { Trip, Order, User, Location } from '../types/trip';

// 模拟位置数据
export const mockLocations: Location[] = [
  {
    latitude: 39.908823,
    longitude: 116.397470,
    address: '北京市东城区天安门广场',
    name: '天安门广场',
    city: '北京市',
    district: '东城区',
  },
  {
    latitude: 39.916668,
    longitude: 116.397026,
    address: '北京市东城区故宫博物院',
    name: '故宫博物院',
    city: '北京市',
    district: '东城区',
  },
  {
    latitude: 39.906217,
    longitude: 116.391248,
    address: '北京市西城区北京站',
    name: '北京站',
    city: '北京市',
    district: '西城区',
  },
  {
    latitude: 39.865195,
    longitude: 116.378829,
    address: '北京市丰台区北京南站',
    name: '北京南站',
    city: '北京市',
    district: '丰台区',
  },
  {
    latitude: 40.060717,
    longitude: 116.343307,
    address: '北京市海淀区北京西站',
    name: '北京西站',
    city: '北京市',
    district: '海淀区',
  },
];

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    name: '张三',
    phone: '13800138001',
    gender: 'male',
    age: 28,
    rating: 4.8,
    verified: true,
    carInfo: {
      brand: '大众',
      model: '朗逸',
      color: '白色',
      plateNumber: '京A12345',
      seats: 5,
    },
  },
  {
    id: '2',
    name: '李四',
    phone: '13800138002',
    gender: 'female',
    age: 25,
    rating: 4.9,
    verified: true,
  },
  {
    id: '3',
    name: '王五',
    phone: '13800138003',
    gender: 'male',
    age: 32,
    rating: 4.7,
    verified: false,
    carInfo: {
      brand: '本田',
      model: '雅阁',
      color: '黑色',
      plateNumber: '京B67890',
      seats: 5,
    },
  },
];

// 模拟行程数据
export const mockTrips: Trip[] = [
  {
    id: '1',
    type: 'driver',
    status: 'pending',
    driver: mockUsers[0],
    passengers: [],
    startLocation: mockLocations[0],
    endLocation: mockLocations[1],
    departureTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后
    price: 15,
    seats: 4,
    availableSeats: 4,
    description: '准时出发，不吸烟，可以聊天',
    requirements: ['不吸烟', '准时出发', '可聊天'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    type: 'driver',
    status: 'confirmed',
    driver: mockUsers[2],
    passengers: [mockUsers[1]],
    startLocation: mockLocations[2],
    endLocation: mockLocations[3],
    departureTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4小时后
    price: 25,
    seats: 3,
    availableSeats: 2,
    description: '舒适出行，安静环境',
    requirements: ['不带宠物', '安静出行'],
    createdAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1小时前创建
    updatedAt: new Date().toISOString(),
  },
  {
    id: '3',
    type: 'passenger',
    status: 'pending',
    passengers: [mockUsers[1]],
    startLocation: mockLocations[3],
    endLocation: mockLocations[4],
    departureTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6小时后
    price: 20,
    seats: 2,
    availableSeats: 2,
    description: '寻找顺风车，可以分摊油费',
    requirements: ['女性优先'],
    createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30分钟前创建
    updatedAt: new Date().toISOString(),
  },
];

// 模拟订单数据
export const mockOrders: Order[] = [
  {
    id: '1',
    tripId: '1',
    trip: mockTrips[0],
    passenger: mockUsers[1],
    driver: mockUsers[0],
    status: 'pending',
    seats: 1,
    totalPrice: 15,
    createdAt: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10分钟前创建
    updatedAt: new Date().toISOString(),
  },
  {
    id: '2',
    tripId: '2',
    trip: mockTrips[1],
    passenger: mockUsers[1],
    driver: mockUsers[2],
    status: 'confirmed',
    seats: 1,
    totalPrice: 25,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前创建
    updatedAt: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1小时前更新
  },
  {
    id: '3',
    tripId: '1',
    trip: {
      ...mockTrips[0],
      status: 'completed',
      departureTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天
    },
    passenger: mockUsers[1],
    driver: mockUsers[0],
    status: 'completed',
    seats: 1,
    totalPrice: 15,
    createdAt: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(), // 昨天创建
    updatedAt: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(), // 昨天更新
  },
];

// 模拟搜索结果
export const getMockSearchResults = (searchParams: any): Trip[] => {
  // 简单的模拟搜索逻辑
  return mockTrips.filter(trip => 
    trip.type === 'driver' && 
    trip.status === 'pending' &&
    trip.availableSeats > 0
  );
};

// 模拟API响应延迟
export const mockApiDelay = (ms: number = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 生成随机行程数据
export const generateRandomTrip = (): Trip => {
  const drivers = mockUsers.filter(user => user.carInfo);
  const driver = drivers[Math.floor(Math.random() * drivers.length)];
  const startLocation = mockLocations[Math.floor(Math.random() * mockLocations.length)];
  let endLocation = mockLocations[Math.floor(Math.random() * mockLocations.length)];
  
  // 确保起点和终点不同
  while (endLocation.latitude === startLocation.latitude && endLocation.longitude === startLocation.longitude) {
    endLocation = mockLocations[Math.floor(Math.random() * mockLocations.length)];
  }
  
  const departureTime = new Date(Date.now() + Math.random() * 24 * 60 * 60 * 1000).toISOString();
  const price = Math.floor(Math.random() * 50) + 10;
  const seats = Math.floor(Math.random() * 4) + 1;
  
  return {
    id: Math.random().toString(36).substr(2, 9),
    type: 'driver',
    status: 'pending',
    driver,
    passengers: [],
    startLocation,
    endLocation,
    departureTime,
    price,
    seats,
    availableSeats: seats,
    description: '舒适出行，欢迎拼车',
    requirements: ['不吸烟', '准时出发'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
};

// 生成多个随机行程
export const generateRandomTrips = (count: number): Trip[] => {
  return Array.from({ length: count }, () => generateRandomTrip());
};
